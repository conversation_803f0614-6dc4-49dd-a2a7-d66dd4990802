<div class="theme-offer">
   <?php
     // POST and update the customizer and other related data
    if ( isset( $_POST['submit'] ) ) {

        // Check if Classic Blog Grid plugin is installed
        if (!is_plugin_active('classic-blog-grid/classic-blog-grid.php')) {
            // Plugin slug and file path for Classic Blog Grid
            $web_development_company_plugin_slug = 'classic-blog-grid';
            $web_development_company_plugin_file = 'classic-blog-grid/classic-blog-grid.php';
        
            // Check if Classic Blog Grid is installed and activated
            if ( ! is_plugin_active( $web_development_company_plugin_file ) ) {
        
                // Check if Classic Blog Grid is installed
                $web_development_company_installed_plugins = get_plugins();
                if ( ! isset( $web_development_company_installed_plugins[ $web_development_company_plugin_file ] ) ) {
        
                    // Include necessary files to install plugins
                    include_once( ABSPATH . 'wp-admin/includes/plugin-install.php' );
                    include_once( ABSPATH . 'wp-admin/includes/file.php' );
                    include_once( ABSPATH . 'wp-admin/includes/misc.php' );
                    include_once( ABSPATH . 'wp-admin/includes/class-wp-upgrader.php' );
        
                    // Download and install Classic Blog Grid
                    $web_development_company_upgrader = new Plugin_Upgrader();
                    $web_development_company_upgrader->install( 'https://downloads.wordpress.org/plugin/classic-blog-grid.latest-stable.zip' );
                }
        
                // Activate the Classic Blog Grid plugin after installation (if needed)
                activate_plugin( $web_development_company_plugin_file );
            }
        }

        // ------- Create Main Menu --------
        $web_development_company_menuname = 'Primary Menu';
        $web_development_company_bpmenulocation = 'primary';
        $web_development_company_menu_exists = wp_get_nav_menu_object( $web_development_company_menuname );
    
        if ( !$web_development_company_menu_exists ) {
            $web_development_company_menu_id = wp_create_nav_menu( $web_development_company_menuname );

            // Create Home Page
            $web_development_company_home_title = 'Home';
            $web_development_company_home = array(
                'post_type'    => 'page',
                'post_title'   => $web_development_company_home_title,
                'post_content' => '',
                'post_status'  => 'publish',
                'post_author'  => 1,
                'post_slug'    => 'home'
            );
            $web_development_company_home_id = wp_insert_post($web_development_company_home);
            // Assign Home Page Template
            add_post_meta($web_development_company_home_id, '_wp_page_template', '/template-home-page.php');
            // Update options to set Home Page as the front page
            update_option('page_on_front', $web_development_company_home_id);
            update_option('show_on_front', 'page');
            // Add Home Page to Menu
            wp_update_nav_menu_item($web_development_company_menu_id, 0, array(
                'menu-item-title' => __('Home', 'web-development-company'),
                'menu-item-classes' => 'home',
                'menu-item-url' => home_url('/'),
                'menu-item-status' => 'publish',
                'menu-item-object-id' => $web_development_company_home_id,
                'menu-item-object' => 'page',
                'menu-item-type' => 'post_type'
            ));

            // Create a new Page 
            $web_development_company_pages_title = 'Pages';
            $web_development_company_pages_content = '<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry standard dummy text ever since the 1500, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960 with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>';
            $web_development_company_pages = array(
                'post_type'    => 'page',
                'post_title'   => $web_development_company_pages_title,
                'post_content' => $web_development_company_pages_content,
                'post_status'  => 'publish',
                'post_author'  => 1,
                'post_slug'    => 'pages'
            );
            $web_development_company_pages_id = wp_insert_post($web_development_company_pages);
            // Add Pages Page to Menu
            wp_update_nav_menu_item($web_development_company_menu_id, 0, array(
                'menu-item-title' => __('Pages', 'web-development-company'),
                'menu-item-classes' => 'pages',
                'menu-item-url' => home_url('/pages/'),
                'menu-item-status' => 'publish',
                'menu-item-object-id' => $web_development_company_pages_id,
                'menu-item-object' => 'page',
                'menu-item-type' => 'post_type'
            ));

            // Create About Us Page with Dummy Content
            $web_development_company_about_title = 'About Us';
            $web_development_company_about_content = '<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry standard dummy text ever since the 1500, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960 with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>';
            $web_development_company_about = array(
                'post_type'    => 'page',
                'post_title'   => $web_development_company_about_title,
                'post_content' => $web_development_company_about_content,
                'post_status'  => 'publish',
                'post_author'  => 1,
                'post_slug'    => 'about-us'
            );
            $web_development_company_about_id = wp_insert_post($web_development_company_about);
            // Add About Us Page to Menu
            wp_update_nav_menu_item($web_development_company_menu_id, 0, array(
                'menu-item-title' => __('About Us', 'web-development-company'),
                'menu-item-classes' => 'about-us',
                'menu-item-url' => home_url('/about-us/'),
                'menu-item-status' => 'publish',
                'menu-item-object-id' => $web_development_company_about_id,
                'menu-item-object' => 'page',
                'menu-item-type' => 'post_type'
            ));

            // Assign the menu to the primary location if not already set
            if ( ! has_nav_menu( $web_development_company_bpmenulocation ) ) {
                $web_development_company_locations = get_theme_mod( 'nav_menu_locations' );
                if ( empty( $web_development_company_locations ) ) {
                    $web_development_company_locations = array();
                }
                $web_development_company_locations[ $web_development_company_bpmenulocation ] = $web_development_company_menu_id;
                set_theme_mod( 'nav_menu_locations', $web_development_company_locations );
            }
        }

        set_theme_mod( 'web_developer_the_custom_logo', esc_url( get_template_directory_uri().'/images/Logo.png'));

        //Header Section
        set_theme_mod( 'web_developer_hide_header_section', true);
        set_theme_mod( 'web_developer_address_location_text', 'ADDRESS');
        set_theme_mod( 'web_developer_address_location', 'New York, US');
        set_theme_mod( 'web_developer_email_address_text', 'EMAIL');
        set_theme_mod( 'web_developer_email_address', '<EMAIL>');
        set_theme_mod( 'web_developer_phone_number_text', 'PHONE');
        set_theme_mod( 'web_developer_phone_number', '+************');

        //Slider Section
        set_theme_mod( 'web_developer_hide_categorysec', true);
        set_theme_mod( 'web_developer_button_text', 'Start Now');
        
        $web_developer_slider_category_id = wp_create_category('slider');

        // Set the category in theme mods for the slider section
        set_theme_mod('web_developer_slidersection', $web_developer_slider_category_id); // Update with correct category ID
        
        // Titles for the three posts
        $web_developer_titles = array(
            'Creating a Best Software Solution',
            'Innovative Web Development Services',
            'Custom Apps Built for Your Business'
        );        
        // Create three demo posts and assign them to the 'Slider' category
        for ($web_developer_i = 1; $web_developer_i <= 3; $web_developer_i++) {
            $web_developer_title = $web_developer_titles[$web_developer_i - 1];
            $web_developer_content = 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.';
        
            // Prepare the post object
            $web_developer_my_post = array(
                'post_title'    => wp_strip_all_tags($web_developer_title),
                'post_content'  => $web_developer_content,
                'post_status'   => 'publish',
                'post_type'     => 'post',
                'post_category' => array($web_developer_slider_category_id),
            );
        
            // Insert the post into the database
            $web_developer_post_id = wp_insert_post($web_developer_my_post);
        
            // If the post was successfully created, set the featured image
            if ($web_developer_post_id && !is_wp_error($web_developer_post_id)) {
                // Set the image URL based on the current slider index
                $web_developer_image_url = esc_url(get_stylesheet_directory_uri() . '/images/Slider' . $web_developer_i . '.png');
                $web_developer_upload_dir = wp_upload_dir();
        
                // Download the image data using wp_remote_get()
                $web_developer_response = wp_remote_get($web_developer_image_url);
                if (!is_wp_error($web_developer_response)) {
                    $web_developer_image_data = wp_remote_retrieve_body($web_developer_response);
        
                    if (!empty($web_developer_image_data)) {
                        // Handle the file upload process
                        $web_developer_image_name = 'slider' . $web_developer_i . '.png';
                        $web_developer_unique_file_name = wp_unique_filename($web_developer_upload_dir['path'], $web_developer_image_name);
                        $web_developer_file = $web_developer_upload_dir['path'] . '/' . $web_developer_unique_file_name;
        
                        // Save the image file to the uploads directory
                        global $wp_filesystem;
                        WP_Filesystem();
                        $wp_filesystem->put_contents($web_developer_file, $web_developer_image_data);
        
                        // Check file type and prepare for attachment
                        $web_developer_wp_filetype = wp_check_filetype($web_developer_unique_file_name, null);
                        $web_developer_attachment = array(
                            'post_mime_type' => $web_developer_wp_filetype['type'],
                            'post_title'     => sanitize_file_name($web_developer_unique_file_name),
                            'post_content'   => '',
                            'post_status'    => 'inherit',
                        );
        
                        // Insert the image into the media library and set it as the post's featured image
                        $web_developer_attach_id = wp_insert_attachment($web_developer_attachment, $web_developer_file, $web_developer_post_id);
                        $web_developer_attach_data = wp_generate_attachment_metadata($web_developer_attach_id, $web_developer_file);
                        wp_update_attachment_metadata($web_developer_attach_id, $web_developer_attach_data);
                        set_post_thumbnail($web_developer_post_id, $web_developer_attach_id);
                    }
                }
            }
        }

       //Services Section
       set_theme_mod( 'web_developer_disabled_pgboxes', true);
       
       $web_developer_service_category_id = wp_create_category('services');

       // Set the category in theme mods for the Services section
       set_theme_mod('web_developer_services_cat', $web_developer_service_category_id); // Update with correct category ID
       
       // Titles for the three posts
       $web_developer_titles = array(
           'Infrastructure Plan',
           'Firewall Advance',
           'Flexible Solutions'
       ); 

       $web_development_company_custom_icons = array("fa-solid fa-file-pen", "fa-solid fa-earth-americas", "fa-solid fa-plane-departure");         
       // Create three demo posts and assign them to the 'Services' category
       for ($web_developer_i = 1; $web_developer_i <= 3; $web_developer_i++) {
           $web_developer_title = $web_developer_titles[$web_developer_i - 1];
           $web_developer_content = 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.';
       
           // Prepare the post object
           $web_developer_my_post = array(
               'post_title'    => wp_strip_all_tags($web_developer_title),
               'post_content'  => $web_developer_content,
               'post_status'   => 'publish',
               'post_type'     => 'post',
               'post_category' => array($web_developer_service_category_id),
           );
       
           // Insert the post into the database
           $web_developer_post_id = wp_insert_post($web_developer_my_post);
       
           // If the post was successfully created, set the featured image
           if ($web_developer_post_id && !is_wp_error($web_developer_post_id)) {
               // Set the image URL based on the current service index
               $web_developer_image_url = esc_url(get_stylesheet_directory_uri() . '/images/services' . $web_developer_i . '.png');
               $web_developer_upload_dir = wp_upload_dir();
       
               // Download the image data using wp_remote_get()
               $web_developer_response = wp_remote_get($web_developer_image_url);
               if (!is_wp_error($web_developer_response)) {
                   $web_developer_image_data = wp_remote_retrieve_body($web_developer_response);
       
                   if (!empty($web_developer_image_data)) {
                       // Handle the file upload process
                       $web_developer_image_name = 'services' . $web_developer_i . '.png';
                       $web_developer_unique_file_name = wp_unique_filename($web_developer_upload_dir['path'], $web_developer_image_name);
                       $web_developer_file = $web_developer_upload_dir['path'] . '/' . $web_developer_unique_file_name;
       
                       // Save the image file to the uploads directory
                       global $wp_filesystem;
                       WP_Filesystem();
                       $wp_filesystem->put_contents($web_developer_file, $web_developer_image_data);
       
                       // Check file type and prepare for attachment
                       $web_developer_wp_filetype = wp_check_filetype($web_developer_unique_file_name, null);
                       $web_developer_attachment = array(
                           'post_mime_type' => $web_developer_wp_filetype['type'],
                           'post_title'     => sanitize_file_name($web_developer_unique_file_name),
                           'post_content'   => '',
                           'post_status'    => 'inherit',
                       );
       
                       // Insert the image into the media library and set it as the post's featured image
                       $web_developer_attach_id = wp_insert_attachment($web_developer_attachment, $web_developer_file, $web_developer_post_id);
                       $web_developer_attach_data = wp_generate_attachment_metadata($web_developer_attach_id, $web_developer_file);
                       wp_update_attachment_metadata($web_developer_attach_id, $web_developer_attach_data);
                       set_post_thumbnail($web_developer_post_id, $web_developer_attach_id);
                   }
               }
           }

           update_post_meta($web_developer_post_id, 'web_development_company_custom_icon', $web_development_company_custom_icons[$web_developer_i - 1]);
       }
        
    // Show success message and the "View Site" button
         echo '<div class="success">Demo Import Successful</div>';
    }
     ?>
    <ul>
        <li>
        <hr>
        <?php 
        // Check if the form is submitted
        if ( !isset( $_POST['submit'] ) ) : ?>
           <!-- Show demo importer form only if it's not submitted -->
           <?php echo esc_html( 'Click on the below content to get demo content installed.', 'web-development-company' ); ?>
          <br>
          <small><b><?php echo esc_html('Please take a backup if your website is already live with data. This importer will overwrite existing data.', 'web-development-company' ); ?></b></small>
          <br><br>

          <form id="demo-importer-form" action="" method="POST" onsubmit="return confirm('Do you really want to do this?');">
            <input type="submit" name="submit" value="<?php echo esc_attr('Run Importer','web-development-company'); ?>" class="button button-primary button-large">
          </form>
        <?php 
        endif; 

        // Show "View Site" button after form submission
        if ( isset( $_POST['submit'] ) ) {
        echo '<div class="view-site-btn">';
        echo '<a href="' . esc_url(home_url()) . '" class="button button-primary button-large" style="margin-top: 10px;" target="_blank">View Site</a>';
        echo '</div>';
        }
        ?>

        <hr>
        </li>
    </ul>
 </div>