<?php
/**
 * The template for displaying the footer.
 *
 * Contains the closing of the #content div and all content after
 *
 * @package Web Development Company
 */
?>
<div id="footer">
	<?php 
    $web_developer_footer_widget_enabled = get_theme_mod('web_developer_footer_widget', true);
    
    if ($web_developer_footer_widget_enabled !== false && $web_developer_footer_widget_enabled !== '') { ?>

    <?php 
      $web_developer_widget_areas = get_theme_mod('web_developer_footer_widget_areas', '4');
      if ($web_developer_widget_areas == '3') {
          $web_developer_cols = 'col-lg-4 col-md-6';
      } elseif ($web_developer_widget_areas == '4') {
          $web_developer_cols = 'col-lg-3 col-md-6';
      } elseif ($web_developer_widget_areas == '2') {
          $web_developer_cols = 'col-lg-6 col-md-6';
      } else {
          $web_developer_cols = 'col-lg-12 col-md-12';
      }
    ?>

    <div class="footer-widget">
      <div class="container">
        <div class="row">
            <!-- Footer 1 -->
            <div class="<?php echo esc_attr($web_developer_cols); ?> footer-block">
                <?php if (is_active_sidebar('footer-1')) : ?>
                    <?php dynamic_sidebar('footer-1'); ?>
                <?php else : ?>
                    <aside id="categories" class="widget pb-3" role="complementary" aria-label="<?php esc_attr_e('footer1', 'web-development-company'); ?>">
                        <h3 class="widget-title"><?php esc_html_e('Categories', 'web-development-company'); ?></h3>
                        <ul>
                            <?php wp_list_categories('title_li='); ?>
                        </ul>
                    </aside>
                <?php endif; ?>
            </div>

            <!-- Footer 2 -->
            <div class="<?php echo esc_attr($web_developer_cols); ?> footer-block">
                <?php if (is_active_sidebar('footer-2')) : ?>
                    <?php dynamic_sidebar('footer-2'); ?>
                <?php else : ?>
                    <aside id="archives" class="widget pb-3" role="complementary" aria-label="<?php esc_attr_e('footer2', 'web-development-company'); ?>">
                        <h3 class="widget-title"><?php esc_html_e('Archives', 'web-development-company'); ?></h3>
                        <ul>
                            <?php wp_get_archives(array('type' => 'monthly')); ?>
                        </ul>
                    </aside>
                <?php endif; ?>
            </div>

            <!-- Footer 3 -->
            <div class="<?php echo esc_attr($web_developer_cols); ?> footer-block">
                <?php if (is_active_sidebar('footer-3')) : ?>
                    <?php dynamic_sidebar('footer-3'); ?>
                <?php else : ?>
                    <aside id="meta" class="widget pb-3" role="complementary" aria-label="<?php esc_attr_e('footer3', 'web-development-company'); ?>">
                        <h3 class="widget-title"><?php esc_html_e('Meta', 'web-development-company'); ?></h3>
                        <ul>
                            <?php wp_register(); ?>
                            <li><?php wp_loginout(); ?></li>
                            <?php wp_meta(); ?>
                        </ul>
                    </aside>
                <?php endif; ?>
            </div>

            <!-- Footer 4 -->
            <div class="<?php echo esc_attr($web_developer_cols); ?> footer-block">
                <?php if (is_active_sidebar('footer-4')) : ?>
                    <?php dynamic_sidebar('footer-4'); ?>
                <?php else : ?>
                    <aside id="search-widget" class="widget pb-3" role="complementary" aria-label="<?php esc_attr_e('footer4', 'web-development-company'); ?>">
                        <h3 class="widget-title"><?php esc_html_e('Search', 'web-development-company'); ?></h3>
                        <?php the_widget('WP_Widget_Search'); ?>
                    </aside>
                <?php endif; ?>
            </div>
        </div>
      </div>
    </div>

    <?php } ?>
    <div class="clear"></div>
    
  <div class="copywrap text-center">
    <div class="container">
       <p>
        <a href="<?php 
          $web_developer_copyright_link = get_theme_mod('web_developer_copyright_link', '');
          if (empty($web_developer_copyright_link)) {
              echo esc_url('https://www.theclassictemplates.com/products/free-web-development-wordpress-theme');
          } else {
              echo esc_url($web_developer_copyright_link);
          } ?>" target="_blank">
          <?php echo esc_html(get_theme_mod('web_development_company_copyright_line', __('Web Development Company WordPress Theme', 'web-development-company'))); ?>
        </a> 
        <?php echo esc_html('By Classic Templates', 'web-development-company'); ?>
      </p>
      </div>
  </div>
</div>

<?php if(get_theme_mod('web_developer_scroll_hide',true)){ ?>
   <a id="button"><?php echo esc_html( get_theme_mod('web_developer_scroll_text',__('TOP', 'web-development-company' )) ); ?></a>
<?php } ?>
  
<?php wp_footer(); ?>
</body>
</html>