<?php
/**
 * Code Sunny - Web Development Company Theme
 *
 * @package Code_Sunny
 */

if ( ! defined( '_S_VERSION' ) ) {
	define( '_S_VERSION', '1.0.0' );
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function codesunny_setup() {
	// Make theme available for translation
	load_theme_textdomain( 'codesunny', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head
	add_theme_support( 'automatic-feed-links' );

	// Let WordPress manage the document title
	add_theme_support( 'title-tag' );

	// Enable support for Post Thumbnails
	add_theme_support( 'post-thumbnails' );

	// Add custom image sizes
	add_image_size( 'codesunny-hero', 1200, 600, true );
	add_image_size( 'codesunny-case-study', 800, 400, true );
	add_image_size( 'codesunny-service', 400, 300, true );

	// Register navigation menus
	register_nav_menus( array(
		'primary' => esc_html__( 'Primary Menu', 'codesunny' ),
		'footer' => esc_html__( 'Footer Menu', 'codesunny' ),
	) );

	// Switch default core markup to output valid HTML5
	add_theme_support( 'html5', array(
		'search-form',
		'comment-form',
		'comment-list',
		'gallery',
		'caption',
		'style',
		'script',
	) );

	// Set up the WordPress core custom background feature
	add_theme_support( 'custom-background', array(
		'default-color' => 'ffffff',
		'default-image' => '',
	) );

	// Add theme support for selective refresh for widgets
	add_theme_support( 'customize-selective-refresh-widgets' );

	// Add support for core custom logo
	add_theme_support( 'custom-logo', array(
		'height'      => 60,
		'width'       => 200,
		'flex-width'  => true,
		'flex-height' => true,
	) );

	// Add support for custom header
	add_theme_support( 'custom-header', array(
		'default-image' => '',
		'width' => 1200,
		'height' => 600,
		'flex-width' => true,
		'flex-height' => true,
	) );

	// Add support for editor styles
	add_theme_support( 'editor-styles' );
	add_editor_style( 'css/editor-style.css' );

	// Add support for wide alignment
	add_theme_support( 'align-wide' );

	// Add support for responsive embeds
	add_theme_support( 'responsive-embeds' );

	// Add support for WooCommerce
	add_theme_support( 'woocommerce' );
	add_theme_support( 'wc-product-gallery-zoom' );
	add_theme_support( 'wc-product-gallery-lightbox' );
	add_theme_support( 'wc-product-gallery-slider' );
}
add_action( 'after_setup_theme', 'codesunny_setup' );

function web_development_company_widgets_init() {
	register_sidebar( array(
		'name'          => __( 'Blog Sidebar', 'web-development-company' ),
		'description'   => __( 'Appears on blog page sidebar', 'web-development-company' ),
		'id'            => 'sidebar-1',
		'before_widget' => '<aside id="%1$s" class="widget %2$s">',
		'after_widget'  => '</aside>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	) );

	$web_developer_widget_areas = get_theme_mod('web_developer_footer_widget_areas', '4');
	for ($web_developer_i=1; $web_developer_i <= 4; $web_developer_i++) {
		register_sidebar( array(
			'name'          => __( 'Footer Widget ', 'web-development-company' ) . $web_developer_i,
			'id'            => 'footer-' . $web_developer_i,
			'description'   => '',
			'before_widget' => '<aside id="%1$s" class="widget %2$s">',
			'after_widget'  => '</aside>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		) );
	}
}
add_action( 'widgets_init', 'web_development_company_widgets_init' );

add_action( 'wp_enqueue_scripts', 'web_development_company_enqueue_styles' );

function web_development_company_enqueue_styles() {
    $parenthandle = 'web-development-company-style'; // This is 'twentyfifteen-style' for the Twenty Fifteen theme.
    $web_development_company_color_scheme_css = get_theme_mod('web_development_company_color_scheme_css');
    $theme = wp_get_theme();
    wp_enqueue_style( $parenthandle, esc_url(get_template_directory_uri()) . '/style.css',
        array(),  // if the parent theme code has a dependency, copy it to here
        $theme->parent()->get('Version')
    );
    wp_enqueue_style( 'web-development-company-child-style', get_stylesheet_uri(),
        array( $parenthandle ),
        $theme->get('Version') // this only works if you have Version in the style header
    );

    require get_parent_theme_file_path( '/inc/color-scheme/custom-color-control.php' );
	wp_add_inline_style( 'web-development-company-style',$web_developer_color_scheme_css );
	require get_theme_file_path( '/inc/color-scheme/custom-color-control.php' );
	wp_add_inline_style( 'web-development-company-style',$web_development_company_color_scheme_css);

}

// customizer css
function web_development_company_enqueue_customizer_css() {
    wp_enqueue_style( 'web_development_company-customizer-css', get_stylesheet_directory_uri() . '/css/customize-controls.css' );
}
add_action( 'customize_controls_print_styles', 'web_development_company_enqueue_customizer_css' );

function web_development_company_scripts() {

	wp_enqueue_style( 'web-development-company-responsive', esc_url(get_theme_file_uri())."/css/responsive.css" );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}

	// font-family
	$web_development_company_headings_font = esc_html(get_theme_mod('web_development_company_headings_fonts'));
	$web_development_company_body_font = esc_html(get_theme_mod('web_development_company_body_fonts'));

	if ($web_development_company_headings_font) {
	    wp_enqueue_style('web-development-company-headings-fonts', 'https://fonts.googleapis.com/css?family=' . urlencode($web_development_company_headings_font));
	} else {
	    wp_enqueue_style('open-sans-headings', 'https://fonts.googleapis.com/css?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800');
	}

	if ($web_development_company_body_font) {
	    wp_enqueue_style('web-development-company-body-fonts', 'https://fonts.googleapis.com/css?family=' . urlencode($web_development_company_body_font));
	} else {
	    wp_enqueue_style('open-sans-body', 'https://fonts.googleapis.com/css?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800');
	}

}
add_action( 'wp_enqueue_scripts', 'web_development_company_scripts' );

add_action( 'customize_register', 'web_development_company_customize_register', 11 );

function web_development_company_customize_register() {
	global $wp_customize;

	$wp_customize->remove_setting( 'web_developer_main_text' );
	$wp_customize->remove_control( 'web_developer_main_text' );
	$wp_customize->remove_setting( 'web_developer_main_title' );
	$wp_customize->remove_control( 'web_developer_main_title' );
	$wp_customize->remove_setting( 'web_developerservicemaintext_color' );
	$wp_customize->remove_control( 'web_developerservicemaintext_color' );
	$wp_customize->remove_setting( 'web_developerservicemaintitle_color' );
	$wp_customize->remove_control( 'web_developerservicemaintitle_color' );

	$wp_customize->remove_setting( 'web_developerfootercopytext_color' );
	$wp_customize->remove_control( 'web_developerfootercopytext_color' );
	$wp_customize->remove_setting( 'web_developerfootercopytexthover_color' );
	$wp_customize->remove_control( 'web_developerfootercopytexthover_color' );

	$wp_customize->remove_setting( 'web_developerbuttonbg_color' );
	$wp_customize->remove_control( 'web_developerbuttonbg_color' );
	$wp_customize->remove_setting( 'web_developerbuttonbghover_color' );
	$wp_customize->remove_control( 'web_developerbuttonbghover_color' );
	$wp_customize->remove_setting( 'web_developerboxopacity_color' );
	$wp_customize->remove_control( 'web_developerboxopacity_color' );
	$wp_customize->remove_setting( 'web_developer_copyright_line' );
	$wp_customize->remove_control( 'web_developer_copyright_line' );	

}

/*-- Custom metafield --*/
function web_development_company_custom_price() {
    add_meta_box( 'bn_meta', __( 'Web Development Company Meta Fields', 'web-development-company' ), 'web_development_company_render_custom_icon_meta_field', 'post', 'normal', 'high' );
}
if (is_admin()){
    add_action('admin_menu', 'web_development_company_custom_price');
}

function web_development_company_render_custom_icon_meta_field($post) {
    wp_nonce_field(basename(__FILE__), 'web_development_company_custom_icon_meta_nonce');
    $custom_icon_value = get_post_meta($post->ID, 'web_development_company_custom_icon', true);
    ?>

    <label for="web_development_company_custom_icon_field">Icon Class:</label>
    <input type="text" name="web_development_company_custom_icon_field" id="web_development_company_custom_icon_field" value="<?php echo esc_attr($custom_icon_value); ?>" />
    <p>Example: fas fa-globe</p>

    <?php if (!empty($custom_icon_value)) : ?>
        <div class="custom-icon-preview">
            <i class="<?php echo esc_attr($custom_icon_value); ?>"></i>
        </div>
    <?php endif; ?>

    <?php
}

function web_development_company_save_custom_icon_meta($post_id) {
    if (!isset($_POST['web_development_company_custom_icon_meta_nonce']) || !wp_verify_nonce($_POST['web_development_company_custom_icon_meta_nonce'], basename(__FILE__))) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['web_development_company_custom_icon_field'])) {
        update_post_meta($post_id, 'web_development_company_custom_icon', sanitize_text_field($_POST['web_development_company_custom_icon_field']));
    }
}
add_action('save_post', 'web_development_company_save_custom_icon_meta');


// Customizer Section
function web_development_company_customizer ( $wp_customize ) {

   // Global Color	
   
	$wp_customize->add_setting('web_development_company_color_scheme_gradiant2', array(
		'default'           => '#5b97ff',
		'sanitize_callback' => 'sanitize_hex_color',
	));
	$wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'web_development_company_color_scheme_gradiant2', array(
		'label'    => __('Theme Color', 'web-development-company'),
		'section'  => 'web_developer_global_color',
		'settings' => 'web_development_company_color_scheme_gradiant2',
	)));	
  
	$wp_customize->add_setting('web_development_company_copyright_line',array(
		'default' => '',
		'sanitize_callback' => 'sanitize_text_field',
	));
	$wp_customize->add_control( 'web_development_company_copyright_line', array(
		'section' 	=> 'web_developer_footer',
		'label'	 	=> __('Copyright Line','web-developer'),
		'type'    	=> 'text',
		'priority' 	=> null,
	));
 
}
add_action( 'customize_register', 'web_development_company_customizer' );

if ( ! function_exists( 'web_developer_the_custom_logo' ) ) :
	/**
	 * Displays the optional custom logo.
	 *
	 * Does nothing if the custom logo is not available.
	 *
	 * @since web-development-company
	 */
	function web_developer_the_custom_logo() {
		if ( function_exists( 'the_custom_logo' ) ) {
			// Check if custom logo is set via the customizer
			if ( has_custom_logo() ) {
				the_custom_logo();
			} else {
				// If no custom logo, display the logo from the child theme directory
				echo "<a href='" . esc_url( home_url() ) . "' rel='home'>
						<img src='" . esc_url( get_stylesheet_directory_uri() ) . "/images/Logo.png' alt='" . esc_attr__( 'Web Development Company logo', 'web-development-company' ) . "' />
					  </a>";
			}
		}
	}
endif;


// Redirect to a custom page after theme activation in the child theme
function web_development_company_options() {
    global $pagenow;

    if ( is_admin() && 'themes.php' === $pagenow && isset( $_GET['activated'] ) && current_user_can( 'manage_options' ) ) {
        // Theme activated — future actions can go here, like setting theme mods or showing notices.
    }
}
add_action( 'admin_init', 'web_development_company_options' );

// Add extra theme info page in the child theme
function web_development_company_theme_info_menu_link() {

    $web_development_company_theme = wp_get_theme();
    
    // Add the new "Theme Demo Import" page
    add_theme_page(
        esc_html__( 'Theme Demo Import', 'web-development-company' ),
        esc_html__( 'Theme Demo Import', 'web-development-company' ),
        'edit_theme_options',
        'web-development-company-demo',
        'web_development_company_demo_content_page'
    );
}
add_action( 'admin_menu', 'web_development_company_theme_info_menu_link' );

function web_development_company_hide_parent_demo_menu() {
    remove_submenu_page( 'themes.php', 'web-developer-demo' );
}
add_action( 'admin_menu', 'web_development_company_hide_parent_demo_menu', 999 );

// Function to render the custom "Theme Demo Import" page
function web_development_company_demo_content_page() {
    $web_development_company_theme = wp_get_theme();
    ?>
    <div class="container">
       <div class="start-box">
          <div class="columns-wrapper m-0"> 
             <div class="column column-half clearfix">
               <div class="wrapper-info"> 
                  <img src="<?php echo esc_url( get_stylesheet_directory_uri().'/images/Logo.png' ); ?>" alt="Web Development Company Logo" />
                  <h2><?php esc_html_e( 'Welcome to Web Development Company', 'web-development-company' ); ?></h2>
                  <span class="version"><?php esc_html_e( 'Version', 'web-development-company' ); ?>: <?php echo esc_html( $web_development_company_theme->get( 'Version' ) ); ?></span>	
                  <p><?php esc_html_e( 'To get started, click the button below to import all demo content.', 'web-development-company' ); ?></p>
                  <?php 
                  // Include the demo content if available
                  if ( file_exists( get_stylesheet_directory() . '/inc/demo-content.php' ) ) {
                      require get_stylesheet_directory() . '/inc/demo-content.php';
                  }
                  ?>
               </div>
             </div>
             <div class="column column-half clearfix">
                <div class="get-screenshot">
                    <img src="<?php echo esc_url( get_stylesheet_directory_uri().'/screenshot.png' ); ?>" alt="Web Development Company Screenshot" />
                </div>   
             </div>
          </div>
       </div>
    </div>
    <?php
}

// Load additional admin styles for the child theme
function web_development_company_admin_enqueue_scripts() {
    wp_enqueue_style( 'web-development-company-admin-style', get_stylesheet_directory_uri() . '/css/addon.css' );
}
add_action( 'admin_enqueue_scripts', 'web_development_company_admin_enqueue_scripts' );
?>

