/* Mobile Portrait View */
@media screen and (max-width:767px) {
  .catwrapslider .owl-next{
  	right: 82% !important;
  }
  .top_header .border-right{
    margin-bottom: 10px;
  }
  .slider-img-color, .slidesection img{
    height: 400px !important;
  }
  .slide-btn {
    margin-top: 20px !important;
  }
}
@media screen and (min-width:767px) and (max-width:999px){
  .catwrapslider .owl-next{
  	right: 88% !important;
  }
  #catsliderarea .owl-prev{
  	right: 5% !important;
  }
  .catwrapslider .owl-prev, .catwrapslider .owl-next{
    top: 50% !important;
  }
}
@media screen and (max-width: 1000px){
  .slider-box{
  	left: 15%;
  	right: 15%;
  }
  .sidenav{
    background-color: var(--first-theme-color) !important;
  }
}
@media screen and (min-width:1000px) and (max-width:1024px){
  .header .search_box label {
      width: 75%;
  }
  .slider-box{
    right: 55%;
  }
  #catsliderarea .owl-next, #catsliderarea .owl-prev{
    left: 5% !important;
  }
}