/*
Theme Name: Web Development Company
Theme URI: https://www.theclassictemplates.com/products/free-web-development-wordpress-theme
Author: classictemplate
Author URI: https://www.theclassictemplates.com/
Description: Web Development Company WordPress theme is a purpose-built theme specifically designed for web development companies and agencies. It offers a comprehensive set of features and functionalities to showcase the services and expertise of web development businesses engagingly and professionally. With its modern design, Web Development Company provides a visually appealing platform to highlight the capabilities and portfolio of web development companies. The theme offers multiple pre-designed layouts and templates that can be easily customized to match the branding and style of the company. It allows for the seamless integration of logos, color schemes, and typography, ensuring a consistent and cohesive representation of the brand. Furthermore, the theme offers dedicated sections and layouts to present past projects, client testimonials, and success stories. This enables web development companies to demonstrate their expertise and capabilities, giving potential clients a clear understanding of their quality of work. The theme also includes sections highlighting services, team members, and client logos, providing a comprehensive overview of the company's offerings and credentials. The integration of contact forms and call-to-action buttons facilitates easy communication and encourages visitors to reach out for inquiries or project discussions. In addition, the Web Development Company theme is fully responsive and mobile-friendly. It ensures that the website looks and functions flawlessly on various devices and screen sizes, providing an optimal user experience for visitors accessing the site from desktops, tablets, or smartphones.
Template: web-developer
Version: 1.9.2
Tested up to: 6.8
Requires PHP: 5.6
Requires at least: 5.0
License: GNU General Public License
License URI: http://www.gnu.org/licenses/gpl.html
Text Domain: web-development-company
Tags: blog, e-commerce, portfolio, one-column, two-columns, three-columns, four-columns, grid-layout, left-sidebar, right-sidebar, custom-logo, post-formats, translation-ready, full-width-template, footer-widgets, featured-images, custom-colors, editor-style, wide-blocks, block-styles, custom-header, custom-background, custom-menu, sticky-post, threaded-comments, theme-options, rtl-language-support

Web Development Company WordPress Theme has been created by classictemplate (theclassictemplates.com), 2023.
Web Development Company WordPress Theme is released under the terms of GNU GPL

Web Development Company WordPress Theme is child theme of Web Developer WordPress Theme, Copyright 2023 classictemplate (theclassictemplates.com)
Web Developer WordPress Theme is distributed under the terms of the GNU GPL
*/
body{
    font-family: 'Open Sans', sans-serif;
    overflow-x: hidden;
    overflow-y: scroll;
}
:root {
    --first-theme-color: #1e29ed;
    --second-theme-color: #5b97ff;
}

  a, a:hover{
      text-decoration: none !important;
      color:var(--first-theme-color);
  }
  a:hover,p.site-title a:hover, h1.site-title a:hover{
      color: var(--first-theme-color);
  }
  /*braedcrumb*/
  .breadcrumb a{
    color: var(--first-theme-color) !important;
    border: 2px solid var(--first-theme-color) !important;
    background-image: linear-gradient(0deg, var(--first-theme-color),var(--second-theme-color) );
  }
  .breadcrumb .current-breadcrumb, .breadcrumb a:hover{
    background-color:#1E29F0;
    color:#fff !important;
  }
  /*header*/
  .header, .page-template-template-home-page .header {
      background: var(--first-theme-color);
  }
  #mySidenav nav#site-navigation{
      background:none;
  }
  #head-banner{
      background-color: #00888b;
  }
  p.site-title a, h1.site-title a{
      font-weight: 700;
      font-size: 22px;
  }
  .main-nav a{
      font-size: 15px;
      font-weight: 500;
  }
  .main-nav a:hover{
      color: #fff;
  }
  .main-nav ul ul a:hover {
      color: var(--first-theme-color);
  }
  .top_header .top-text{
      font-weight: 700;
  }
  /*block css*/
  .site-main .wp-block-button a:hover, 
  .site-main .wp-block-button.is-style-outline .wp-block-button__link:not(.has-background):hover,
  .postsec-list .wp-block-button a:hover, 
  .postsec-list .wp-block-button.is-style-outline .wp-block-button__link:not(.has-background):hover{
    background: none;
    border:1px solid var(--first-theme-color);
    color: var(--first-theme-color);
  }
  .site-main .wp-block-button__link, .postsec-list .wp-block-button__link{
    background-image: linear-gradient(0deg, var(--first-theme-color),var(--second-theme-color) );
    color:#fff !important;
    border-radius: 30px;
    margin-bottom: 10px;
    border:1px solid var(--first-theme-color);
  }
  .site-main .wp-block-button.is-style-outline a, .postsec-list .wp-block-button.is-style-outline a{
    background:none;
    border:1px solid var(--first-theme-color) !important;
    color: var(--first-theme-color) !important;
  }
  /*slider*/
  .slidesection{
      -webkit-mask-image:none;
      background-color: #000;
      height: 600px;
  }
  .slidesection img{
      opacity: 0.5;
      height: 600px !important;
      object-fit: cover;
  }
  .bg-opacity {
      width: auto;
      height: auto;
      position: static;
      background: #000;
  }
  .catwrapslider .owl-next {
      right: 95%;
      left: auto;
  }
  .catwrapslider .owl-prev {
      right: 2%;
      left: auto;
  }
  .catwrapslider .owl-prev, .catwrapslider .owl-next{
      font-size: 32px;
  }
  button.owl-prev span, button.owl-next span{
      top: 0;
      font-size: 28px;
  }
  #catsliderarea .owl-next{
      left: 3%;
  }
  #catsliderarea .owl-prev{
      right: 3%;
  }
  .slide-btn a, .service-btn a {
      color: #ffffff !important;
      background-image: linear-gradient(0deg, var(--first-theme-color),var(--second-theme-color) );
      border-radius: 30px;
      display: inline-block;
  }
  .postsec-list input.search-submit:hover, .slide-btn a:hover, .service-btn a:hover, .pagemore a:hover, .serv-btn a:hover, .woocommerce ul.products li.product .button:hover, .woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce a.button:hover, .woocommerce button.button:hover, #commentform input#submit:hover{
      background-color: #fff;
      color: var(--first-theme-color);
      background-image: none;
  }
  .postsec-list input.search-submit:hover, .pagemore a:hover, .serv-btn a:hover, .woocommerce ul.products li.product .button:hover, .woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce a.button:hover, .woocommerce button.button:hover, #commentform input#submit:hover{
      border:1px solid var(--first-theme-color);
  }
  .toggle-nav button, h2.woocommerce-loop-product__title, #sidebar .tagcloud a, .top_header i, .listarticle h2 a:hover, #sidebar ul li a:hover, .ftr-4-box ul li a:hover, .ftr-4-box ul li.current_page_item a{
      color: var(--first-theme-color);
  }
  .listarticle, aside.widget, #sidebar .tagcloud a{
      border-color: var(--first-theme-color);
  }
  span.search_box input.search-submit, .catwrapslider .owl-prev:hover, .catwrapslider .owl-next:hover{
      background-color: var(--first-theme-color);
  }
  .woocommerce span.onsale{
      background-color: var(--first-theme-color);
  }
  nav.woocommerce-MyAccount-navigation ul li:hover{
      background-color: #000 !important;
  }
  .postmeta a:hover{
    color:var(--first-theme-color) !important;
  }
  a.wc-block-components-totals-coupon-link, a.components-button.wc-block-components-button.wp-element-button.wc-block-cart__submit-button.contained, a.wc-block-components-checkout-return-to-cart-button, .wc-block-components-totals-coupon__button.contained, button.wc-block-components-checkout-place-order-button{
    
  }
  .slider-box h1 a:hover{
    color: var(--first-theme-color);
  }
  /*service*/
  .service-btn a{
      padding: 10px 25px;
      font-size: 16px;
  }
  .services_inner_box h3 a{
      font-size: 23px;
      font-weight: bold;
  }
  .services_inner_box{
      border: 1px solid #eee;
      padding: 18px;
      margin-bottom: 5em !important;
  }
  .services_inner_box:hover, #button{
      background-image: linear-gradient(0deg, var(--first-theme-color),var(--second-theme-color) );
  }
  .services_inner_box:hover h3 a, .services_inner_box:hover p{
      color: #fff;
  }
  .services_inner_box:hover .service-btn a{
      background-color: #fff !important;
      color: var(--first-theme-color) !important;
      background-image: none;
  }
  .services_inner_box p{
      line-height: 25px;
  }
  .services_inner_box{
      margin-top: -13%;
  }
  .meta-feilds {
      text-align: center;
      background-image: linear-gradient(0deg, var(--first-theme-color),var(--second-theme-color) );
      padding: 20px;
      transform: skew(5deg);
      margin-left: 50px;
      display: inline-block;
      border-radius: 7px;
      position: relative;
      margin-bottom: 30px;
  }
  .meta-feilds i{
      color: #fff;
      font-size: 45px;
  }
  input.search-submit, .banner-btn a, .pagemore a, .serv-btn a, .woocommerce ul.products li.product .button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce a.button, .woocommerce button.button, .woocommerce #respond input#submit, #commentform input#submit, .woocommerce a.added_to_cart, .woocommerce-account .addresses .title .edit {
      color: #ffffff !important;
      background-image: linear-gradient(0deg, var(--first-theme-color),var(--second-theme-color) );
      border-radius: 30px;
      border:none;
      padding: 15px 35px;
      font-weight: 600;
      font-size: 12px;
  }
  nav.woocommerce-MyAccount-navigation ul li{
      border: solid 2px var(--first-theme-color) !important;
      background-color: var(--first-theme-color) !important;
  }
  .postsec-list input.search-submit {
      padding: 15px 40px;
  }
  nav.woocommerce-MyAccount-navigation ul li a{
      color: #fff !important;
  }
  .pagemore a{
      text-decoration: none !important;
  }
  .woocommerce a.added_to_cart {
    padding: 12px 25px !important;
    font-weight: 600;
  }
  /*sidebar*/
  #sidebar .widget a,
  #sidebar .widget a:visited {
      color:#212529;
  }
  #sidebar input[type="text"],
  #sidebar input.search-submit,
  #footer input.search-submit,.tagcloud a:hover,
  form.woocommerce-product-search button, input.search-submit, .widget_calendar caption , .widget_calendar #today, .woocommerce a.added_to_cart:hover{
      background-color: var(--first-theme-color);
      color: #fff;
  }
  .header-top, .category-btn, .product-search button[type="submit"], span.item-count, .pagemore:hover, .shop-now a:hover, #commentform input#submit:hover, #sidebar input.search-submit, form.woocommerce-product-search button, .woocommerce ul.products li.product .button:hover, .woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce a.button:hover, .woocommerce button.button:hover, nav.woocommerce-MyAccount-navigation ul li:hover, .catwrapslider .owl-prev:hover, .catwrapslider .owl-next:hover, .woocommerce-account .addresses .title .edit:hover{
      background-color: var(--first-theme-color);
      color: #fff !important;
  }
  .woocommerce-account .addresses .title .edit{
    border: none !important;
    color: #fff !important;
  }
  #sidebar .widget a:hover,
  #sidebar .widget a:active, .widget .tagcloud a:hover{
      color: var(--first-theme-color);
  }
  #sidebar ul li::before{
      color: var(--first-theme-color) !important;
  }
  #sidebar .widget, .tagcloud a:hover, .widget .tagcloud a:hover,
  #sidebar input[type="search"],
  #footer input[type="search"]{
      border-color: var(--first-theme-color);
  }
  span.page-numbers.current, .nav-links .page-numbers:hover{
    background-color: var(--first-theme-color);
    color:#fff;
    border:1px solid var(--first-theme-color);
  }
  .pagemore a{
      display: inline-block;
      border-radius: 30px;
  }
  .pagemore:hover{
      background:none !important;
  }
  /*clearing css*/
  .page-links a, .page-links span{
    padding: 10px 15px;
    background: none !important;
    color:#000;
    border:1px solid #000;
  }
  .page-links .post-page-numbers.current, .page-links a:hover{
    background-color: var(--first-theme-color) !important;
    color: #fff;
    border:1px solid var(--first-theme-color) !important;
  }
  /*footer*/
  .copywrap{
      background-color: var(--first-theme-color);
  }
  #footer .copywrap a, #footer .copywrap p{
      color: #fff;
  }

  .ftr-4-box h5 span{
      color:var(--first-theme-color);
  }
  #sidebar .wp-block-search__label{
    font-weight: 500;
  }
  .site-main .wp-block-button.is-style-outline a{
    color:#fff !important;
  }