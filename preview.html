<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Company - Theme Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --first-theme-color: #1e29ed;
            --second-theme-color: #5b97ff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            overflow-x: hidden;
            line-height: 1.6;
        }
        
        a {
            text-decoration: none;
            color: var(--first-theme-color);
        }
        
        a:hover {
            color: var(--first-theme-color);
        }
        
        /* Header */
        .header {
            background: var(--first-theme-color);
            color: white;
            padding: 1rem 0;
        }
        
        .top-header {
            background-color: #00888b;
            padding: 0.5rem 0;
            font-size: 14px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .top-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .contact-info {
            display: flex;
            gap: 2rem;
            color: white;
        }
        
        .contact-info span {
            font-weight: 700;
        }
        
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        
        .logo img {
            height: 50px;
        }
        
        .main-nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .main-nav a {
            color: white;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .main-nav a:hover {
            color: #fff;
        }
        
        /* Slider Section */
        .slider-section {
            position: relative;
            height: 500px;
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('web-development-company/images/Slider1.png');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            color: white;
        }
        
        .slider-content {
            max-width: 600px;
        }
        
        .slider-content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .slider-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .btn {
            background: var(--first-theme-color);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: var(--second-theme-color);
        }
        
        /* Services Section */
        .services-section {
            padding: 4rem 0;
            background: #f8f9fa;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .service-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
        }
        
        .service-card i {
            font-size: 3rem;
            color: var(--first-theme-color);
            margin-bottom: 1rem;
        }
        
        .service-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .service-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .read-more {
            color: var(--first-theme-color);
            font-weight: 600;
        }
        
        /* Footer */
        .footer {
            background: #333;
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        .footer p {
            margin: 0;
        }
        
        .footer a {
            color: var(--first-theme-color);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .main-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .main-nav ul {
                flex-direction: column;
                text-align: center;
            }
            
            .slider-content h1 {
                font-size: 2rem;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <div class="container">
            <div class="top-header-content">
                <div class="contact-info">
                    <div><span>ADDRESS:</span> New York, US</div>
                    <div><span>EMAIL:</span> <EMAIL></div>
                    <div><span>PHONE:</span> +************</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="header">
        <div class="container">
            <div class="main-header">
                <div class="logo">
                    <img src="web-development-company/images/Logo.png" alt="Web Development Company Logo">
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#portfolio">Portfolio</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Slider Section -->
    <section class="slider-section" id="home">
        <div class="container">
            <div class="slider-content">
                <h1>Professional Web Development</h1>
                <p>We create stunning websites and web applications that help your business grow and succeed in the digital world.</p>
                <button class="btn">Start Now</button>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section" id="services">
        <div class="container">
            <div class="text-center">
                <h2>Our Services</h2>
                <p>We offer comprehensive web development solutions</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <i class="fa-solid fa-file-pen"></i>
                    <h3>Web Design</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-earth-americas"></i>
                    <h3>Web Development</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-plane-departure"></i>
                    <h3>Digital Marketing</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>
                <a href="https://www.theclassictemplates.com/products/free-web-development-wordpress-theme" target="_blank">
                    Web Development Company WordPress Theme
                </a> 
                By Classic Templates
            </p>
        </div>
    </footer>
</body>
</html>
