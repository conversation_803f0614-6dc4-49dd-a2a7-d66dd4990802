<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Company - Theme Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --first-theme-color: #1e29ed;
            --second-theme-color: #5b97ff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            overflow-x: hidden;
            line-height: 1.6;
        }
        
        a {
            text-decoration: none;
            color: var(--first-theme-color);
        }
        
        a:hover {
            color: var(--first-theme-color);
        }
        
        /* Header */
        .header {
            background: var(--first-theme-color);
            color: white;
            padding: 1rem 0;
        }
        

        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        

        
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        
        .logo img {
            height: 50px;
        }
        
        .main-nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .main-nav a {
            color: white;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .main-nav a:hover {
            color: #fff;
        }
        
        /* Hero Section */
        .hero-section {
            position: relative;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--first-theme-color) 0%, var(--second-theme-color) 100%);
            display: flex;
            align-items: center;
            color: white;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,800 1000,1000"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            min-height: 80vh;
        }

        .hero-text {
            max-width: 600px;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 20px;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            font-weight: 800;
            line-height: 1.2;
        }

        .hero-title .highlight {
            color: #FFD700;
            position: relative;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2.5rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: #FFD700;
            color: #1e29ed;
            padding: 15px 35px;
            border: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary:hover {
            background: #FFC107;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 15px 35px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255,255,255,0.1);
            border-color: white;
            transform: translateY(-2px);
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-image {
            width: 100%;
            max-width: 500px;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
            transition: transform 0.3s ease;
        }

        .hero-image:hover {
            transform: perspective(1000px) rotateY(-10deg) rotateX(2deg);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 80%;
            right: 5%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255,255,255,0.2);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #FFD700;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.5rem;
        }
        
        /* Services Section */
        .services-section {
            padding: 4rem 0;
            background: #f8f9fa;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .service-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
        }
        
        .service-card i {
            font-size: 3rem;
            color: var(--first-theme-color);
            margin-bottom: 1rem;
        }
        
        .service-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .service-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .read-more {
            color: var(--first-theme-color);
            font-weight: 600;
        }
        
        /* Footer */
        .footer {
            background: #333;
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        .footer p {
            margin: 0;
        }
        
        .footer a {
            color: var(--first-theme-color);
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .stats-row {
                grid-template-columns: repeat(3, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .main-header {
                flex-direction: column;
                gap: 1rem;
            }

            .main-nav ul {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .hero-section {
                min-height: 90vh;
                padding: 2rem 0;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-buttons {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .btn-primary, .btn-secondary {
                width: 100%;
                max-width: 280px;
                text-align: center;
            }



            .stats-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .btn-primary, .btn-secondary {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Main Header -->
    <header class="header">
        <div class="container">
            <div class="main-header">
                <div class="logo">
                    <img src="web-development-company/images/Logo.png" alt="Web Development Company Logo">
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#portfolio">Portfolio</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-badge">
                        🚀 #1 Web Development Agency
                    </div>
                    <h1 class="hero-title">
                        We Build <span class="highlight">Digital Experiences</span> That Drive Results
                    </h1>
                    <p class="hero-subtitle">
                        Transform your business with cutting-edge web solutions. We create stunning websites, powerful web applications, and digital strategies that help you dominate your market.
                    </p>
                    <div class="hero-buttons">
                        <a href="#contact" class="btn-primary">Start Your Project</a>
                        <a href="#portfolio" class="btn-secondary">View Our Work</a>
                    </div>
                    <div class="stats-row">
                        <div class="stat-item">
                            <span class="stat-number">500+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">98%</span>
                            <span class="stat-label">Client Satisfaction</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">Support Available</span>
                        </div>
                    </div>
                </div>
                <div class="hero-visual">
                    <img src="web-development-company/images/Slider1.png" alt="Web Development Services" class="hero-image">
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section" id="services">
        <div class="container">
            <div class="text-center">
                <h2>Our Services</h2>
                <p>We offer comprehensive web development solutions</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <i class="fa-solid fa-file-pen"></i>
                    <h3>Web Design</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-earth-americas"></i>
                    <h3>Web Development</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-plane-departure"></i>
                    <h3>Digital Marketing</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>
                <a href="https://www.theclassictemplates.com/products/free-web-development-wordpress-theme" target="_blank">
                    Web Development Company WordPress Theme
                </a> 
                By Classic Templates
            </p>
        </div>
    </footer>
</body>
</html>
