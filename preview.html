<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Company - Theme Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1976D2;
            --secondary-blue: #2196F3;
            --primary-purple: #4A4A8A;
            --secondary-purple: #6B46C1;
            --accent-gold: #FFD700;
            --dark-bg: #0f0f23;
            --light-text: #ffffff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            overflow-x: hidden;
            line-height: 1.6;
        }
        
        a {
            text-decoration: none;
            color: var(--primary-blue);
        }

        a:hover {
            color: var(--secondary-blue);
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 50%, var(--primary-blue) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(74, 74, 138, 0.3);
        }
        

        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        

        
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        
        .logo img {
            height: 60px;
            transition: transform 0.3s ease;
        }

        .logo img:hover {
            transform: scale(1.05);
        }
        
        .main-nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .main-nav a {
            color: white;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .main-nav a:hover {
            color: #fff;
        }
        
        /* Hero Section */
        .hero-section {
            position: relative;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--dark-bg) 0%, var(--primary-purple) 25%, var(--secondary-purple) 50%, var(--primary-blue) 75%, var(--secondary-blue) 100%);
            display: flex;
            align-items: center;
            color: white;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(74, 74, 138, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(25, 118, 210, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(107, 70, 193, 0.2) 0%, transparent 50%);
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .hero-content {
            position: relative;
            z-index: 3;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5rem;
            align-items: center;
            min-height: 85vh;
        }

        .hero-text {
            max-width: 650px;
            animation: slideInLeft 1s ease-out;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, rgba(25,118,210,0.3) 0%, rgba(74,74,138,0.2) 100%);
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 0.95rem;
            font-weight: 700;
            margin-bottom: 2.5rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(25,118,210,0.4);
            box-shadow: 0 8px 32px rgba(25,118,210,0.2);
        }

        .hero-badge::before {
            content: '💻';
            font-size: 1.2rem;
        }

        .hero-title {
            font-size: 4.2rem;
            margin-bottom: 2rem;
            font-weight: 900;
            line-height: 1.1;
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 50%, #FFD700 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .hero-title .highlight {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, var(--accent-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
        }

        .hero-title .highlight::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
            border-radius: 2px;
            animation: underlineGlow 2s ease-in-out infinite alternate;
        }

        @keyframes underlineGlow {
            0% { box-shadow: 0 0 5px var(--primary-blue); }
            100% { box-shadow: 0 0 20px var(--primary-blue), 0 0 30px var(--secondary-blue); }
        }

        .hero-subtitle {
            font-size: 1.4rem;
            margin-bottom: 3rem;
            opacity: 0.95;
            line-height: 1.7;
            font-weight: 400;
            color: #e0e0e0;
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-weight: 800;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.4);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--primary-purple) 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(25, 118, 210, 0.6);
        }

        .btn-primary::after {
            content: '→';
            transition: transform 0.3s ease;
        }

        .btn-primary:hover::after {
            transform: translateX(5px);
        }

        .btn-secondary {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 18px 40px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .btn-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transition: width 0.3s ease;
        }

        .btn-secondary:hover::before {
            width: 100%;
        }

        .btn-secondary:hover {
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255,255,255,0.1);
        }

        .btn-secondary::after {
            content: '▶';
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .btn-secondary:hover::after {
            transform: translateX(3px);
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: slideInRight 1s ease-out;
        }

        .hero-mockup {
            position: relative;
            width: 100%;
            max-width: 550px;
            height: 400px;
            background: linear-gradient(145deg, #2a2a3e 0%, #1a1a2e 100%);
            border-radius: 20px;
            padding: 20px;
            box-shadow:
                0 25px 80px rgba(0,0,0,0.4),
                0 0 0 1px rgba(255,255,255,0.1);
            transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
            transition: all 0.4s ease;
            overflow: hidden;
        }

        .hero-mockup::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(90deg, #ff5f56, #ffbd2e, #27ca3f);
            border-radius: 20px 20px 0 0;
        }

        .hero-mockup::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            width: 12px;
            height: 12px;
            background: #ff5f56;
            border-radius: 50%;
            box-shadow:
                20px 0 0 #ffbd2e,
                40px 0 0 #27ca3f;
        }

        .mockup-content {
            margin-top: 40px;
            height: calc(100% - 40px);
            background: linear-gradient(135deg, #1e1e2e 0%, #2a2a3e 100%);
            border-radius: 10px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .code-lines {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            color: #64ffda;
        }

        .code-line {
            margin-bottom: 8px;
            opacity: 0;
            animation: typeWriter 0.5s ease-out forwards;
        }

        .code-line:nth-child(1) { animation-delay: 0.5s; }
        .code-line:nth-child(2) { animation-delay: 1s; }
        .code-line:nth-child(3) { animation-delay: 1.5s; }
        .code-line:nth-child(4) { animation-delay: 2s; }

        .hero-mockup:hover {
            transform: perspective(1000px) rotateY(-8deg) rotateX(2deg) scale(1.02);
            box-shadow:
                0 35px 100px rgba(0,0,0,0.5),
                0 0 0 1px rgba(255,255,255,0.2);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 80%;
            right: 5%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes slideInLeft {
            0% {
                opacity: 0;
                transform: translateX(-100px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            0% {
                opacity: 0;
                transform: translateX(100px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes typeWriter {
            0% {
                opacity: 0;
                transform: translateX(-10px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-top: 2rem;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
            animation: pulse 3s ease-in-out infinite;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.1);
            box-shadow: 0 10px 30px rgba(25,118,210,0.3);
        }

        .stat-item:nth-child(2) {
            animation-delay: 1s;
        }

        .stat-item:nth-child(3) {
            animation-delay: 2s;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 500;
            color: #e0e0e0;
        }
        
        /* Services Section */
        .services-section {
            padding: 4rem 0;
            background: #f8f9fa;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .service-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
        }
        
        .service-card i {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }
        
        .service-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .service-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .read-more {
            color: var(--primary-blue);
            font-weight: 600;
        }
        
        /* Footer */
        .footer {
            background: #333;
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        .footer p {
            margin: 0;
        }
        
        .footer a {
            color: var(--primary-blue);
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .stats-row {
                grid-template-columns: repeat(3, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .main-header {
                flex-direction: column;
                gap: 1rem;
            }

            .main-nav ul {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .hero-section {
                min-height: 90vh;
                padding: 2rem 0;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-buttons {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .btn-primary, .btn-secondary {
                width: 100%;
                max-width: 280px;
                text-align: center;
            }



            .stats-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .btn-primary, .btn-secondary {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Main Header -->
    <header class="header">
        <div class="container">
            <div class="main-header">
                <div class="logo">
                    <img src="logo.svg" alt="Code Sunny - Web Development Company">
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#portfolio">Portfolio</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-badge">
                        Code Sunny - Premium Development Agency
                    </div>
                    <h1 class="hero-title">
                        We Build <span class="highlight">Innovative Solutions</span> That Drive Success
                    </h1>
                    <p class="hero-subtitle">
                        Transform your business with Code Sunny's expertise. We create powerful web applications, stunning websites, and cutting-edge digital solutions that help you achieve your goals and dominate your market.
                    </p>
                    <div class="hero-buttons">
                        <a href="#contact" class="btn-primary">Start Your Project</a>
                        <a href="#portfolio" class="btn-secondary">View Portfolio</a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="hero-mockup">
                        <div class="mockup-content">
                            <div class="code-lines">
                                <div class="code-line">const codeSunny = new WebAgency();</div>
                                <div class="code-line">codeSunny.design('innovative').develop('fast');</div>
                                <div class="code-line">codeSunny.optimize('performance');</div>
                                <div class="code-line">codeSunny.launch('success'); // ☀️</div>
                            </div>
                        </div>
                    </div>
                    <div class="floating-elements">
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                        <div class="floating-element"></div>
                    </div>
                </div>
            </div>
            <div class="stats-row">
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">Projects Delivered</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">98%</span>
                    <span class="stat-label">Client Satisfaction</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">Expert Support</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section" id="services">
        <div class="container">
            <div class="text-center">
                <h2>Our Services</h2>
                <p>We offer comprehensive web development solutions</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <i class="fa-solid fa-file-pen"></i>
                    <h3>Web Design</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-earth-americas"></i>
                    <h3>Web Development</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-plane-departure"></i>
                    <h3>Digital Marketing</h3>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text.</p>
                    <a href="#" class="read-more">Read More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>
                <a href="#" target="_blank">
                    Code Sunny - Web Development Agency
                </a>
                © 2024 All Rights Reserved
            </p>
        </div>
    </footer>
</body>
</html>
