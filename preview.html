<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Development Company - Theme Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="hero-fix.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1976D2;
            --secondary-blue: #2196F3;
            --primary-purple: #4A4A8A;
            --secondary-purple: #6B46C1;
            --accent-gold: #FFD700;
            --dark-bg: #0f0f23;
            --light-text: #ffffff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            overflow-x: hidden;
            line-height: 1.6;
        }
        
        a {
            text-decoration: none;
            color: var(--primary-blue);
        }

        a:hover {
            color: var(--secondary-blue);
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 50%, var(--primary-blue) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(74, 74, 138, 0.3);
        }
        

        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        

        
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        
        .logo img {
            height: 60px;
            transition: transform 0.3s ease;
        }

        .logo img:hover {
            transform: scale(1.05);
        }
        
        .main-nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .main-nav a {
            color: white;
            font-size: 15px;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .main-nav a:hover {
            color: #fff;
        }
        
        /* Hero Section */
        .hero-section {
            position: relative;
            min-height: 100vh;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f8ff 100%);
            display: flex;
            align-items: center;
            color: #333;
            padding: 6rem 0;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(25, 118, 210, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(74, 74, 138, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(107, 70, 193, 0.05) 0%, transparent 50%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5rem;
            align-items: center;
            min-height: 80vh;
        }

        .hero-left {
            max-width: 600px;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, rgba(25,118,210,0.1) 0%, rgba(74,74,138,0.1) 100%);
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2rem;
            border: 1px solid rgba(25,118,210,0.2);
            color: var(--primary-blue);
        }

        .hero-badge i {
            font-size: 1rem;
        }

        .hero-title {
            font-size: 3.8rem;
            font-weight: 900;
            color: #333;
            margin-bottom: 1.5rem;
            line-height: 1.1;
        }

        .hero-title .highlight {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .hero-title .highlight::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
            border-radius: 2px;
        }

        .hero-description {
            font-size: 1.2rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 2.5rem;
        }

        .hero-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2.5rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95rem;
            font-weight: 600;
            color: #333;
        }

        .feature-item i {
            color: var(--primary-blue);
            font-size: 1rem;
        }

        /* Featured Case Study Section */
        .featured-case-study {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .case-study-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .case-study-label {
            color: var(--primary-blue);
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .case-study-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: #333;
            margin: 1rem 0;
        }

        .case-study-description {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .case-study-meta {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .case-study-meta span {
            color: #666;
            font-weight: 600;
        }

        .case-study-link {
            color: var(--primary-blue);
            font-weight: 600;
            text-decoration: none;
            border-bottom: 2px solid var(--primary-blue);
            padding-bottom: 2px;
        }

        .case-study-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .stat-card .stat-number {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--primary-blue);
            display: block;
            margin-bottom: 1rem;
        }

        .stat-card .stat-label {
            font-size: 1.1rem;
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .stat-note {
            font-size: 0.9rem;
            color: #999;
            font-style: italic;
        }

        /* We Reinvent Section */
        .reinvent-section {
            padding: 6rem 0;
            background: white;
        }

        .reinvent-content {
            text-align: center;
            margin-bottom: 4rem;
        }

        .reinvent-title {
            font-size: 3rem;
            font-weight: 300;
            color: #333;
            margin-bottom: 2rem;
        }

        .reinvent-description {
            font-size: 1.2rem;
            color: #666;
            line-height: 1.7;
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        .reinvent-services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-top: 4rem;
        }

        .service-item {
            text-align: left;
            padding: 2rem;
        }

        .service-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .service-item h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }

        .service-item p {
            color: #666;
            line-height: 1.6;
        }

        /* Site Needs Section */
        .site-needs-section {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .needs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .need-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .need-item i {
            font-size: 1.5rem;
            color: var(--primary-blue);
            min-width: 30px;
        }

        .need-item span {
            font-weight: 600;
            color: #333;
        }

        /* Challenge Section */
        .challenge-section {
            padding: 6rem 0;
            background: white;
        }

        .challenge-title {
            font-size: 3rem;
            font-weight: 300;
            color: #333;
            text-align: center;
            margin-bottom: 1rem;
        }

        .challenge-subtitle {
            font-size: 1.5rem;
            font-weight: 600;
            color: #666;
            text-align: center;
            margin-bottom: 4rem;
        }

        .challenges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
        }

        .challenge-item {
            padding: 2.5rem;
            border-left: 4px solid var(--primary-blue);
            background: #f8f9fa;
        }

        .challenge-item h4 {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }

        .challenge-item p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .challenge-item ul {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }

        .challenge-item ul li {
            color: #666;
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .challenge-item ul li::before {
            content: '•';
            color: var(--primary-blue);
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .challenge-link {
            color: var(--primary-blue);
            font-weight: 600;
            text-decoration: none;
            border-bottom: 2px solid var(--primary-blue);
            padding-bottom: 2px;
        }

        .challenge-link:hover {
            opacity: 0.8;
        }

        /* FAQ Section */
        .faq-section {
            padding: 6rem 0;
            background: #f8f9fa;
        }

        .faq-subtitle {
            font-size: 1.8rem;
            font-weight: 300;
            color: #666;
            text-align: center;
            margin-bottom: 4rem;
        }

        .faq-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .faq-item {
            background: white;
            padding: 2.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .faq-item h4 {
            font-size: 1.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }

        .faq-item p {
            color: #666;
            line-height: 1.6;
        }



        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-top: 2rem;
        }

        .hero-stats .stat-item {
            text-align: center;
        }

        .hero-stats .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 0.5rem;
        }

        .hero-stats .stat-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 600;
        }

        /* Hero Visual */
        .hero-right {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-visual {
            position: relative;
            width: 100%;
            max-width: 500px;
        }

        .hero-mockup {
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            overflow: hidden;
            transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
            transition: transform 0.3s ease;
        }

        .hero-mockup:hover {
            transform: perspective(1000px) rotateY(-10deg) rotateX(2deg);
        }

        .mockup-header {
            background: #f5f5f5;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .mockup-dots {
            display: flex;
            gap: 8px;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27ca3f; }

        .mockup-url {
            background: #ffffff;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #666;
            border: 1px solid #e0e0e0;
        }

        .mockup-content {
            padding: 20px;
        }

        .mockup-nav {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .nav-item {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: #666;
        }

        .nav-item.active {
            background: var(--primary-blue);
            color: white;
        }

        .mockup-hero {
            text-align: center;
            margin-bottom: 25px;
        }

        .mockup-title {
            font-size: 1.2rem;
            font-weight: 800;
            color: #333;
            margin-bottom: 8px;
        }

        .mockup-subtitle {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 15px;
        }

        .mockup-button {
            background: var(--primary-blue);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
        }

        .mockup-features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .feature-box {
            height: 60px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.3);
            animation: float 6s ease-in-out infinite;
        }

        .element-1 {
            top: 10%;
            right: -10%;
            animation-delay: 0s;
        }

        .element-2 {
            top: 30%;
            left: -15%;
            animation-delay: 1.5s;
        }

        .element-3 {
            bottom: 30%;
            right: -5%;
            animation-delay: 3s;
        }

        .element-4 {
            bottom: 10%;
            left: -10%;
            animation-delay: 4.5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-weight: 800;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.4);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--primary-purple) 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(25, 118, 210, 0.6);
        }

        .btn-primary::after {
            content: '→';
            transition: transform 0.3s ease;
        }

        .btn-primary:hover::after {
            transform: translateX(5px);
        }

        .btn-secondary {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 18px 40px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }

        .btn-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: rgba(255,255,255,0.1);
            transition: width 0.3s ease;
        }

        .btn-secondary:hover::before {
            width: 100%;
        }

        .btn-secondary:hover {
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255,255,255,0.1);
        }

        .btn-secondary::after {
            content: '▶';
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .btn-secondary:hover::after {
            transform: translateX(3px);
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: slideInRight 1s ease-out;
        }

        .hero-mockup {
            position: relative;
            width: 100%;
            max-width: 550px;
            height: 400px;
            background: linear-gradient(145deg, #2a2a3e 0%, #1a1a2e 100%);
            border-radius: 20px;
            padding: 20px;
            box-shadow:
                0 25px 80px rgba(0,0,0,0.4),
                0 0 0 1px rgba(255,255,255,0.1);
            transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
            transition: all 0.4s ease;
            overflow: hidden;
        }

        .hero-mockup::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(90deg, #ff5f56, #ffbd2e, #27ca3f);
            border-radius: 20px 20px 0 0;
        }

        .hero-mockup::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            width: 12px;
            height: 12px;
            background: #ff5f56;
            border-radius: 50%;
            box-shadow:
                20px 0 0 #ffbd2e,
                40px 0 0 #27ca3f;
        }

        .mockup-content {
            margin-top: 40px;
            height: calc(100% - 40px);
            background: linear-gradient(135deg, #1e1e2e 0%, #2a2a3e 100%);
            border-radius: 10px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .code-lines {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            color: #64ffda;
        }

        .code-line {
            margin-bottom: 8px;
            opacity: 0;
            animation: typeWriter 0.5s ease-out forwards;
        }

        .code-line:nth-child(1) { animation-delay: 0.5s; }
        .code-line:nth-child(2) { animation-delay: 1s; }
        .code-line:nth-child(3) { animation-delay: 1.5s; }
        .code-line:nth-child(4) { animation-delay: 2s; }

        .hero-mockup:hover {
            transform: perspective(1000px) rotateY(-8deg) rotateX(2deg) scale(1.02);
            box-shadow:
                0 35px 100px rgba(0,0,0,0.5),
                0 0 0 1px rgba(255,255,255,0.2);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 80%;
            right: 5%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes slideInLeft {
            0% {
                opacity: 0;
                transform: translateX(-100px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            0% {
                opacity: 0;
                transform: translateX(100px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes typeWriter {
            0% {
                opacity: 0;
                transform: translateX(-10px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-top: 2rem;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
            animation: pulse 3s ease-in-out infinite;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.1);
            box-shadow: 0 10px 30px rgba(25,118,210,0.3);
        }

        .stat-item:nth-child(2) {
            animation-delay: 1s;
        }

        .stat-item:nth-child(3) {
            animation-delay: 2s;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 500;
            color: #e0e0e0;
        }

        /* Award Section */
        .award-section {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: #333;
            margin-bottom: 3rem;
        }

        .award-content {
            text-align: center;
        }

        .award-content h3 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 3rem;
            font-weight: 700;
        }

        .award-section .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
            padding: 0;
            border: none;
        }

        .award-section .stat-item {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            animation: none;
        }

        .award-section .stat-number {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3rem;
            font-weight: 900;
        }

        .award-section .stat-label {
            color: #666;
            font-weight: 600;
            opacity: 1;
        }

        /* About Section */
        .about-section {
            padding: 4rem 0;
            background: white;
        }

        .about-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .about-content p {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #666;
            margin-bottom: 1.5rem;
        }

        /* Testimonials Section */
        .testimonials-section {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .testimonial-card {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
        }

        .testimonial-content {
            margin-bottom: 1.5rem;
        }

        .testimonial-content p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #333;
            font-style: italic;
        }

        .testimonial-author {
            color: var(--primary-blue);
            font-weight: 600;
        }
        
        /* Services Section */
        .services-section {
            padding: 5rem 0;
            background: white;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2.5rem;
            margin-top: 3rem;
        }
        
        .service-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
        }
        
        .service-card i {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }
        
        .service-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .service-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .read-more {
            color: var(--primary-blue);
            font-weight: 600;
        }
        
        /* Why Choose Us Section */
        .why-choose-section {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card i {
            font-size: 3rem;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
            font-weight: 700;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-blue) 100%);
            color: white;
            text-align: center;
        }

        .cta-content h2 {
            font-size: 3rem;
            margin-bottom: 3rem;
            font-weight: 300;
        }

        /* Footer */
        .footer {
            background: #333;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-logo img {
            height: 50px;
            filter: brightness(0) invert(1);
        }

        .footer-services h4,
        .footer-contact h4 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .footer-services ul {
            list-style: none;
            padding: 0;
        }

        .footer-services ul li {
            margin-bottom: 0.5rem;
        }

        .footer-services ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-services ul li a:hover {
            color: var(--primary-blue);
        }

        .footer-contact p {
            color: #ccc;
            margin: 0;
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .hero-title {
                font-size: 3rem;
            }

            .hero-mockup {
                transform: none;
            }

            .floating-element {
                display: none;
            }

            .stats-row {
                grid-template-columns: repeat(3, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .main-header {
                flex-direction: column;
                gap: 1rem;
            }

            .main-nav ul {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .hero-section {
                min-height: auto;
                padding: 3rem 0;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-features {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .reinvent-title {
                font-size: 2rem;
            }

            .challenge-title {
                font-size: 2rem;
            }

            .challenges-grid {
                grid-template-columns: 1fr;
            }

            .faq-grid {
                grid-template-columns: 1fr;
            }

            .hero-ratings {
                flex-direction: column;
                gap: 1rem;
            }

            .hero-buttons {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .btn-primary, .btn-secondary {
                width: 100%;
                max-width: 280px;
                text-align: center;
            }



            .stats-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .cta-content h2 {
                font-size: 2rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .btn-primary, .btn-secondary {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Main Header -->
    <header class="header">
        <div class="container">
            <div class="main-header">
                <div class="logo">
                    <img src="logo.svg" alt="Code Sunny - Web Development Company">
                </div>
                <nav class="main-nav">
                    <ul>
                        <li><a href="preview.html" class="active">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="case-studies.html">Case Studies</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-left">
                    <div class="hero-badge">
                        <i class="fa-solid fa-rocket"></i>
                        <span>Premium Web Development Agency</span>
                    </div>
                    <h1 class="hero-title">
                        We Build <span class="highlight">Digital Experiences</span> That Drive Results
                    </h1>
                    <p class="hero-description">
                        Transform your business with cutting-edge web solutions. We create stunning websites, powerful web applications, and digital strategies that help you dominate your market and achieve sustainable growth.
                    </p>

                    <div class="hero-features">
                        <div class="feature-item">
                            <i class="fa-solid fa-check-circle"></i>
                            <span>Custom Web Development</span>
                        </div>
                        <div class="feature-item">
                            <i class="fa-solid fa-check-circle"></i>
                            <span>Mobile-First Design</span>
                        </div>
                        <div class="feature-item">
                            <i class="fa-solid fa-check-circle"></i>
                            <span>SEO Optimized</span>
                        </div>
                        <div class="feature-item">
                            <i class="fa-solid fa-check-circle"></i>
                            <span>24/7 Support</span>
                        </div>
                    </div>

                    <div class="hero-buttons">
                        <a href="contact.html" class="btn-primary">
                            <span>Start Your Project</span>
                            <i class="fa-solid fa-arrow-right"></i>
                        </a>
                        <a href="case-studies.html" class="btn-secondary">
                            <i class="fa-solid fa-play"></i>
                            <span>View Our Work</span>
                        </a>
                    </div>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Projects Completed</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">Client Satisfaction</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">5+</div>
                            <div class="stat-label">Years Experience</div>
                        </div>
                    </div>
                </div>

                <div class="hero-right">
                    <div class="hero-visual">
                        <div class="hero-mockup">
                            <div class="mockup-header">
                                <div class="mockup-dots">
                                    <span class="dot red"></span>
                                    <span class="dot yellow"></span>
                                    <span class="dot green"></span>
                                </div>
                                <div class="mockup-url">codesunny.com</div>
                            </div>
                            <div class="mockup-content">
                                <div class="mockup-nav">
                                    <div class="nav-item active">Home</div>
                                    <div class="nav-item">About</div>
                                    <div class="nav-item">Services</div>
                                    <div class="nav-item">Contact</div>
                                </div>
                                <div class="mockup-hero">
                                    <div class="mockup-title">Professional Web Development</div>
                                    <div class="mockup-subtitle">Building the future, one website at a time</div>
                                    <div class="mockup-button">Get Started</div>
                                </div>
                                <div class="mockup-features">
                                    <div class="feature-box"></div>
                                    <div class="feature-box"></div>
                                    <div class="feature-box"></div>
                                </div>
                            </div>
                        </div>

                        <div class="floating-elements">
                            <div class="floating-element element-1">
                                <i class="fa-solid fa-code"></i>
                            </div>
                            <div class="floating-element element-2">
                                <i class="fa-solid fa-palette"></i>
                            </div>
                            <div class="floating-element element-3">
                                <i class="fa-solid fa-rocket"></i>
                            </div>
                            <div class="floating-element element-4">
                                <i class="fa-solid fa-mobile-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Case Study -->
    <section class="featured-case-study">
        <div class="container">
            <div class="case-study-header">
                <span class="case-study-label">Featured Case Study | TechCorp Solutions</span>
                <h2 class="case-study-title">Improving the Digital Experience</h2>
                <p class="case-study-description">TechCorp Solutions needed a web development partner to redesign and rebuild their platform. The results were substantial.</p>
                <div class="case-study-meta">
                    <span>Strategy: UI/UX Design, Web Development, SEO</span>
                    <a href="#" class="case-study-link">Read Full Case Study</a>
                </div>
            </div>
            <div class="case-study-stats">
                <div class="stat-card">
                    <div class="stat-number">45k</div>
                    <div class="stat-label">Website Clicks from Google Search Results</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">148</div>
                    <div class="stat-label">New Keywords in Top 3 Organic Search Position</div>
                    <div class="stat-note">*Results 3 months after website redesign</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">55k</div>
                    <div class="stat-label">New Website Visitors</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">438%</div>
                    <div class="stat-label">Website Traffic Increase</div>
                    <div class="stat-note">*Results 3 months after website redesign</div>
                </div>
            </div>
        </div>
    </section>

    <!-- We Reinvent Section -->
    <section class="reinvent-section">
        <div class="container">
            <div class="reinvent-content">
                <h2 class="reinvent-title">We (re)invent websites.</h2>
                <p class="reinvent-description">
                    As your full service website partner, we work closely with our clients to design and develop transformative user experiences across all devices. Whether you need a lead magnet website, a story-telling experience, or API integrations, we can help you. We love connecting with interesting brands and telling their stories through creative technology.
                </p>
                <a href="#services" class="btn-secondary">View Our Services</a>
            </div>
            <div class="reinvent-services">
                <div class="service-item">
                    <div class="service-number">01.</div>
                    <h3>Website Design</h3>
                    <p>We design and redesign websites that communicate to audiences.</p>
                </div>
                <div class="service-item">
                    <div class="service-number">02.</div>
                    <h3>Website Development</h3>
                    <p>We use modern tech stacks to build modern websites.</p>
                </div>
                <div class="service-item">
                    <div class="service-number">03.</div>
                    <h3>Website Ranking</h3>
                    <p>We implement SEO best practices to help your website rank.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Everything Your Site Needs Section -->
    <section class="site-needs-section">
        <div class="container">
            <h2 class="section-title">Everything your site needs for success</h2>
            <div class="needs-grid">
                <div class="need-item">
                    <i class="fa-solid fa-search"></i>
                    <span>Market Research and Competitor Analysis</span>
                </div>
                <div class="need-item">
                    <i class="fa-solid fa-palette"></i>
                    <span>Custom Web Design</span>
                </div>
                <div class="need-item">
                    <i class="fa-solid fa-code"></i>
                    <span>Website Development & Maintenance</span>
                </div>
                <div class="need-item">
                    <i class="fa-solid fa-chart-line"></i>
                    <span>Website Analytics & Conversion Tracking</span>
                </div>
                <div class="need-item">
                    <i class="fa-solid fa-rocket"></i>
                    <span>Search Engine Optimization (SEO)</span>
                </div>
                <div class="need-item">
                    <i class="fa-solid fa-universal-access"></i>
                    <span>Website Accessibility</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Challenge Accepted Section -->
    <section class="challenge-section">
        <div class="container">
            <h2 class="challenge-title">Challenge Accepted.</h2>
            <h3 class="challenge-subtitle">We solve your website performance bottlenecks</h3>

            <div class="challenges-grid">
                <div class="challenge-item">
                    <h4>My website isn't getting enough traffic and leads</h4>
                    <p>Your website is your 24/7 sales agent. Therefore, ensuring it's properly setup to get healthy and consistent traffic will simplify your organization's marketing and sales efforts. If you want qualified leads to find you on the internet, search engine optimization is required.</p>
                    <ul>
                        <li>Boost your presence in search engines</li>
                        <li>Get discovered organically</li>
                        <li>Turn website visitors into leads</li>
                    </ul>
                    <a href="#" class="challenge-link">Explore SEO Services</a>
                </div>

                <div class="challenge-item">
                    <h4>My website is out-of-date and needs a revamp</h4>
                    <p>Poor website design is one of the leading reasons why users will abandon a website. Investing in a quality design that clearly communicates the value your business offers is a marketing game changer. We redesign websites to clarify messages and streamline your funnel.</p>
                    <ul>
                        <li>Clarify your company message</li>
                        <li>Generate more leads from your website</li>
                        <li>Make every landing page count</li>
                    </ul>
                    <a href="#" class="challenge-link">Learn about Website Redesign</a>
                </div>

                <div class="challenge-item">
                    <h4>My website is too complicated to manage</h4>
                    <p>Do you need a website that can be easily managed and updated by your team? Our WordPress website solutions can help you do just that. Gain independence and empower your team with a web architecture that employs easy-to-use content management systems.</p>
                    <ul>
                        <li>Get tailored low-code to no-code solutions</li>
                        <li>Receive training on your website architecture</li>
                        <li>Easily manage your website in-house</li>
                    </ul>
                    <a href="#" class="challenge-link">Explore WordPress Development</a>
                </div>

                <div class="challenge-item">
                    <h4>My logo and brand is out-dated and unimpressive</h4>
                    <p>Clean and modern assets, such as a logo, color palette, and style guide, are the building blocks of a strong website. In order to ensure you have the best web design possible, auditing your creative assets is a critical first step.</p>
                    <ul>
                        <li>Modernize your brand</li>
                        <li>Unify your professional identity across the internet</li>
                        <li>Standardize future asset development</li>
                    </ul>
                    <a href="#" class="challenge-link">Explore Our Design Portfolio</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <h2 class="section-title">Our Clients Love Us</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The Best Web Development firm. We are extremely happy with the results. They have done a great job for our NYC-based business and take care of all our development needs."</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>John Smith</strong>, CEO TechCorp
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"We're extremely pleased with Code Sunny's work on our website design, development, and excellent performance optimization. Highly recommend their services!"</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>Sarah Johnson</strong>, Founder StartupXYZ
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"We're thrilled with Code Sunny's excellent development work for our business. Their creative and effective strategies have propelled us to success."</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>Mike Davis</strong>, Director InnovateLab
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section" id="services">
        <div class="container">
            <h2 class="section-title">Our Proven Web Development Expertise</h2>
            <div class="services-grid">
                <div class="service-card">
                    <i class="fa-solid fa-code"></i>
                    <h3>Web Development</h3>
                    <p>Build modern, responsive, and high-performance websites with our expert development team using latest technologies and best practices.</p>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-palette"></i>
                    <h3>Web Design & UI/UX</h3>
                    <p>We create modern, user-friendly designs that engage your audience and convert visitors into customers with stunning visual experiences.</p>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-mobile-alt"></i>
                    <h3>Mobile App Development</h3>
                    <p>Develop powerful mobile applications for iOS and Android platforms that provide seamless user experiences and drive business growth.</p>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-shopping-cart"></i>
                    <h3>E-commerce Solutions</h3>
                    <p>Build robust e-commerce platforms that drive sales and provide exceptional shopping experiences for your customers.</p>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-search"></i>
                    <h3>SEO Optimization</h3>
                    <p>Optimize your website for search engines to increase visibility, drive organic traffic, and improve your online presence.</p>
                </div>
                <div class="service-card">
                    <i class="fa-solid fa-tools"></i>
                    <h3>Maintenance & Support</h3>
                    <p>Keep your website running smoothly with our comprehensive maintenance and 24/7 support services for peace of mind.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose-section">
        <div class="container">
            <h2 class="section-title">Why Choose Code Sunny</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <i class="fa-solid fa-chart-line"></i>
                    <h3>Proven Results</h3>
                    <p>We're a results-driven agency. Our strategies and techniques are backed by a proven track record of success in elevating our client's online presence.</p>
                </div>
                <div class="feature-card">
                    <i class="fa-solid fa-user-cog"></i>
                    <h3>Personalized Approach</h3>
                    <p>We understand that each business is unique. Our team tailors every strategy to your specific needs, ensuring the best possible outcome for your brand.</p>
                </div>
                <div class="feature-card">
                    <i class="fa-solid fa-award"></i>
                    <h3>Award-Winning Agency</h3>
                    <p>Our expertise has been recognized by industry leaders. We're a multi-award-winning agency committed to excellence in web development.</p>
                </div>
                <div class="feature-card">
                    <i class="fa-solid fa-users"></i>
                    <h3>In-House Experts</h3>
                    <p>Our team is comprised of highly skilled professionals with vast experience in all areas of web development. You get the benefit of a whole team's expertise.</p>
                </div>
                <div class="feature-card">
                    <i class="fa-solid fa-cogs"></i>
                    <h3>Full Service Agency</h3>
                    <p>From web design to mobile apps and from SEO to e-commerce, we offer comprehensive web development solutions under one roof.</p>
                </div>
                <div class="feature-card">
                    <i class="fa-solid fa-heart"></i>
                    <h3>Client-First Philosophy</h3>
                    <p>Your success is our priority. We're dedicated to achieving your business objectives and delivering measurable results that drive growth.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title">Website Development Company</h2>
            <h3 class="faq-subtitle">Frequently Asked Questions</h3>

            <div class="faq-grid">
                <div class="faq-item">
                    <h4>What does a web development company do?</h4>
                    <p>A web development company is a team of professionals that specializes in creating and developing websites for businesses and organizations. We provide website design, development, SEO optimization, maintenance, and ongoing support to help businesses establish a strong online presence.</p>
                </div>

                <div class="faq-item">
                    <h4>How much does it cost to hire an agency to develop a website?</h4>
                    <p>The cost varies based on complexity, features, and customization level. Basic websites start around $2,500-$5,000, while complex e-commerce or custom solutions can range from $10,000-$50,000+. We provide detailed quotes based on your specific requirements.</p>
                </div>

                <div class="faq-item">
                    <h4>Why should I hire a web development company?</h4>
                    <p>Hiring a professional web development company ensures expertise, quality, and comprehensive services. We bring specialized skills, stay updated with latest technologies, provide ongoing support, and deliver results that help your business grow online.</p>
                </div>

                <div class="faq-item">
                    <h4>How do I choose a web development company?</h4>
                    <p>Look for a company with a strong portfolio, positive client testimonials, relevant experience in your industry, transparent communication, and comprehensive services. Review their past work, check references, and ensure they understand your business goals.</p>
                </div>

                <div class="faq-item">
                    <h4>What is included in web development?</h4>
                    <p>Web development includes visual design, user interface design, responsive design, front-end and back-end development, SEO optimization, testing, maintenance, and ongoing support. We handle everything from concept to launch and beyond.</p>
                </div>

                <div class="faq-item">
                    <h4>What is the typical timeframe for developing a website?</h4>
                    <p>Timeline depends on project complexity. Simple websites take 4-6 weeks, while complex custom solutions may require 3-6 months. We provide detailed project timelines during our initial consultation and keep you updated throughout the process.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Let's Build the Future Together</h2>
                <a href="#contact" class="btn-primary">Get In Touch</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="logo.svg" alt="Code Sunny">
                </div>
                <div class="footer-services">
                    <h4>Our Services:</h4>
                    <ul>
                        <li><a href="#">Web Development</a></li>
                        <li><a href="#">Web Design & UI/UX</a></li>
                        <li><a href="#">Mobile App Development</a></li>
                        <li><a href="#">E-commerce Solutions</a></li>
                        <li><a href="#">SEO Optimization</a></li>
                        <li><a href="#">Maintenance & Support</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h4>Got a project? Contact us!</h4>
                    <p>© Copyright 2024 | Code Sunny. All Rights Reserved.</p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
