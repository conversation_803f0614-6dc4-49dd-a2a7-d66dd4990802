<?php
/**
 * The Template Name: Home Page
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site will use a
 * different template.
 *
 * @package Web Development Company
 */

get_header(); ?>

<div id="content">
  <?php
    $web_developer_hidcatslide = get_theme_mod('web_developer_hide_categorysec', false);
    $web_developer_slidersection = get_theme_mod('web_developer_slidersection');

    if ($web_developer_hidcatslide && $web_developer_slidersection) { ?>
    <section id="catsliderarea">
      <div class="catwrapslider">
        <div class="owl-carousel">
          <?php if( get_theme_mod('web_developer_slidersection',false) ) { ?>
            <?php $web_developer_queryvar = new WP_Query(
              array( 
                'cat' => esc_attr(get_theme_mod('web_developer_slidersection',false)),
                'posts_per_page' => esc_attr(get_theme_mod('web_developer_slider_count',false))
              )
            );
            while( $web_developer_queryvar->have_posts() ) : $web_developer_queryvar->the_post(); ?>
              <div class="slidesection"> 
                <?php if(has_post_thumbnail()){
                  the_post_thumbnail('full');
                  } else{?>
                  <img src="<?php echo esc_url(get_theme_file_uri()); ?>/images/slider.png" alt="<?php echo esc_attr( 'slider', 'web-development-company'); ?>"/>
                <?php } ?>
                <div class="bg-opacity"></div>
                <div class="slider-box">
                  <h1><a href="<?php echo esc_url( get_permalink() );?>"><?php the_title(); ?></a></h1>
                  <?php
                    $trimexcerpt = get_the_excerpt();
                    $shortexcerpt = wp_trim_words( $trimexcerpt, $num_words = 15 );
                    echo '<p class="mt-4">' . esc_html( $shortexcerpt ) . '</p>'; 
                  ?>
                  <div class="slide-btn mt-lg-4 mt-2">
                    <?php 
                    $web_developer_button_text = get_theme_mod('web_developer_button_text', 'Start Now');
                    $web_developer_button_link_slider = get_theme_mod('web_developer_button_link_slider', ''); 
                    if (empty($web_developer_button_link_slider)) {
                        $web_developer_button_link_slider = get_permalink();
                    }
                    if ($web_developer_button_text || !empty($web_developer_button_link_slider)) { ?>
                      <?php if(get_theme_mod('web_developer_button_text', 'Start Now') != ''){ ?>
                        <a href="<?php echo esc_url($web_developer_button_link_slider); ?>" class="button redmor">
                          <?php echo esc_html($web_developer_button_text); ?>
                            <span class="screen-reader-text"><?php echo esc_html($web_developer_button_text); ?></span>
                        </a>
                      <?php } ?>
                    <?php } ?>
                  </div>
                </div>
              </div>
            <?php endwhile; wp_reset_postdata(); ?>
          <?php } ?>
        </div>
      </div>
      <div class="clear"></div>
    </section>
  <?php } ?>

  <?php
    $web_developer_hidepageboxes = get_theme_mod('web_developer_disabled_pgboxes',false);
    $web_developer_services_cat = get_theme_mod('web_developer_services_cat');
    if( $web_developer_hidepageboxes && $web_developer_services_cat){
  ?>
  <section id="serives_box" class="py-4">
    <div class="container">
      <div class="my-5">
        <div class="row">
          <?php if( get_theme_mod('web_developer_services_cat',false) ) { ?>
            <?php $web_developer_queryvar = new WP_Query('cat='.esc_attr(get_theme_mod('web_developer_services_cat',false)));
              while( $web_developer_queryvar->have_posts() ) : $web_developer_queryvar->the_post(); ?>
                <div class="col-lg-4 col-md-6 col-sm-6">
                  <?php if( get_post_meta(get_the_ID(), 'web_development_company_custom_icon', true) ) {?>
                    <div class="meta-feilds">
                      <?php if( get_post_meta($post->ID, 'web_development_company_custom_icon', true) ) {?>
                        <i class="<?php echo esc_html(get_post_meta($post->ID, 'web_development_company_custom_icon', true)); ?>"></i>
                      <?php }?>
                    </div>
                  <?php }?>
                  <div class="services_inner_box text-center pt-3">
                    <h3 class="mb-3 mt-5"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                    <?php
                      $trimexcerpt = get_the_excerpt();
                      $shortexcerpt = wp_trim_words( $trimexcerpt, $num_words = 15 );
                      echo '<p class="mb-3">' . esc_html( $shortexcerpt ) . '</p>'; 
                    ?>
                    <div class="service-btn mt-3">
                    <a class="text-decoration-none" href="<?php the_permalink(); ?>"><?php esc_html_e('Read More','web-development-company'); ?></a>
                  </div>
                  </div>
                </div>
              <?php endwhile; wp_reset_postdata(); ?>
            <?php } ?>
          <?php }?>
        </div>
      </div>
    </div>
  </section>
</div>

<?php get_footer(); ?>