/* Code Sunny - Web Development Company Styles */

:root {
    --primary-blue: #1976D2;
    --secondary-blue: #2196F3;
    --primary-purple: #4A4A8A;
    --secondary-purple: #6B46C1;
    --accent-gold: #FFD700;
    --dark-bg: #0f0f23;
    --light-text: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    overflow-x: hidden;
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: var(--primary-blue);
}

a:hover {
    color: var(--secondary-blue);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 50%, var(--primary-blue) 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(74, 74, 138, 0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo img {
    height: 60px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    color: white;
    font-size: 15px;
    font-weight: 500;
    transition: color 0.3s;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.main-nav a:hover,
.main-nav a.active {
    color: #fff;
    background: rgba(255,255,255,0.1);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-blue) 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    padding: 18px 40px;
    border: none;
    border-radius: 50px;
    font-weight: 800;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 10px 30px rgba(25, 118, 210, 0.4);
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--primary-purple) 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 40px rgba(25, 118, 210, 0.6);
}

.btn-secondary {
    background: rgba(255,255,255,0.1);
    color: var(--primary-blue);
    padding: 18px 40px;
    border: 2px solid var(--primary-blue);
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.4s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-secondary:hover {
    background: var(--primary-blue);
    color: white;
    transform: translateY(-3px);
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: #333;
    margin-bottom: 3rem;
}

/* About Page Styles */
.about-content-section {
    padding: 5rem 0;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.about-stats .stat-item {
    text-align: center;
}

.about-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-blue);
    display: block;
}

.about-stats .stat-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Mission Section */
.mission-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.mission-content {
    text-align: center;
    margin-bottom: 4rem;
}

.mission-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
}

.mission-content p {
    font-size: 1.2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.7;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-item {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
}

.value-item i {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
}

.value-item h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.value-item p {
    color: #666;
    line-height: 1.6;
}

/* Team Section */
.team-section {
    padding: 5rem 0;
    background: white;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.team-member {
    text-align: center;
    padding: 2rem;
}

.member-image {
    width: 200px;
    height: 200px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.member-role {
    color: var(--primary-blue);
    font-weight: 600;
    margin-bottom: 1rem;
}

.team-member p {
    color: #666;
    line-height: 1.6;
}

/* Services Page Styles */
.services-overview {
    padding: 5rem 0;
    background: white;
}

.overview-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.overview-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
}

.overview-content p {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.7;
}

/* Detailed Services */
.detailed-services {
    padding: 5rem 0;
    background: #f8f9fa;
}

.service-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 5rem;
}

.service-detail.reverse {
    direction: rtl;
}

.service-detail.reverse > * {
    direction: ltr;
}

.service-content {
    padding: 2rem;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.service-content p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.service-content ul {
    list-style: none;
    margin-bottom: 2rem;
}

.service-content ul li {
    color: #666;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.service-content ul li::before {
    content: '✓';
    color: var(--primary-blue);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.service-tech {
    background: #e3f2fd;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-blue);
}

.service-tech span {
    color: #666;
    font-weight: 600;
    font-size: 0.9rem;
}

.service-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Process Steps */
.service-process {
    padding: 5rem 0;
    background: white;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.process-step {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--primary-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 800;
    margin: 0 auto 1.5rem;
}

.process-step h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.process-step p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 50px;
    filter: brightness(0) invert(1);
}

.footer-services h4,
.footer-contact h4 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-weight: 700;
}

.footer-services ul {
    list-style: none;
    padding: 0;
}

.footer-services ul li {
    margin-bottom: 0.5rem;
}

.footer-services ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-services ul li a:hover {
    color: var(--primary-blue);
}

.footer-contact p {
    color: #ccc;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-grid,
    .service-detail {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .service-detail.reverse {
        direction: ltr;
    }
}

@media (max-width: 768px) {
    .main-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-nav ul {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .page-header h1 {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .about-text h2 {
        font-size: 2rem;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Case Studies Styles */
.featured-case-study-detail {
    padding: 5rem 0;
    background: white;
}

.case-study-hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
}

.case-study-category {
    color: var(--primary-blue);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.case-study-hero h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #333;
    margin: 1rem 0;
}

.case-study-hero p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.case-study-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.case-study-meta span {
    color: #666;
    font-size: 0.9rem;
}

.case-study-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.case-study-results {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 15px;
    margin-top: 3rem;
}

.case-study-results h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.result-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.result-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-blue);
    display: block;
    margin-bottom: 0.5rem;
}

.result-label {
    color: #666;
    font-weight: 600;
    font-size: 0.9rem;
}

.case-studies-grid {
    padding: 5rem 0;
    background: #f8f9fa;
}

.studies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.case-study-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.case-study-card:hover {
    transform: translateY(-5px);
}

.case-image {
    height: 200px;
    overflow: hidden;
}

.case-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.case-content {
    padding: 2rem;
}

.case-category {
    color: var(--primary-blue);
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.case-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    margin: 1rem 0;
}

.case-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.case-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.case-stat {
    text-align: center;
}

.case-stat .stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-blue);
    display: block;
}

.case-stat .stat-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 600;
}

.case-link {
    color: var(--primary-blue);
    font-weight: 600;
    text-decoration: none;
    border-bottom: 2px solid var(--primary-blue);
    padding-bottom: 2px;
}

/* Contact Page Styles */
.contact-section {
    padding: 5rem 0;
    background: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1.5rem;
}

.contact-info > p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
    line-height: 1.7;
}

.contact-methods {
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.method-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.method-icon i {
    font-size: 1.5rem;
    color: white;
}

.method-info h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.method-info p {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.method-info span {
    color: #666;
    font-size: 0.9rem;
}

.social-links h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: transform 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px);
    background: var(--secondary-blue);
}

.contact-form h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
}

.project-form {
    background: #f8f9fa;
    padding: 2.5rem;
    border-radius: 15px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
}

.contact-faq {
    padding: 5rem 0;
    background: #f8f9fa;
}

.contact-faq .faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.contact-faq .faq-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-faq .faq-item h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.contact-faq .faq-item p {
    color: #666;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .case-study-hero,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .case-stats {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: 2rem;
    }

    .btn-primary, .btn-secondary {
        padding: 12px 25px;
        font-size: 1rem;
    }
}
