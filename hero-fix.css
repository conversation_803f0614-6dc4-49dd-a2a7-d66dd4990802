/* Hero Section Fix - WebXd Style Override */
.hero-section {
    position: relative !important;
    min-height: 80vh !important;
    background: white !important;
    display: flex !important;
    align-items: center !important;
    color: #333 !important;
    padding: 8rem 0 !important;
    text-align: center !important;
}

.hero-content {
    text-align: center !important;
    max-width: 800px !important;
    margin: 0 auto !important;
}

.hero-title {
    font-size: 4rem !important;
    font-weight: 300 !important;
    color: #333 !important;
    margin-bottom: 2rem !important;
    line-height: 1.2 !important;
}

.hero-subtitle {
    font-size: 2.5rem !important;
    font-weight: 300 !important;
    color: #666 !important;
    margin-bottom: 2rem !important;
    line-height: 1.3 !important;
}

.hero-description {
    font-size: 1.2rem !important;
    color: #666 !important;
    line-height: 1.6 !important;
    margin-bottom: 3rem !important;
    max-width: 700px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.hero-buttons {
    display: flex !important;
    justify-content: center !important;
    gap: 1.5rem !important;
    flex-wrap: wrap !important;
}



/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.8rem !important;
    }
}
