/* Hero Section Fix - Force Override */
.hero-section {
    position: relative !important;
    min-height: 100vh !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f8ff 100%) !important;
    display: flex !important;
    align-items: center !important;
    color: #333 !important;
    padding: 6rem 0 !important;
    overflow: hidden !important;
}

.hero-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: radial-gradient(circle at 20% 80%, rgba(25, 118, 210, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(74, 74, 138, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(107, 70, 193, 0.05) 0%, transparent 50%) !important;
}

.hero-content {
    position: relative !important;
    z-index: 2 !important;
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 5rem !important;
    align-items: center !important;
    min-height: 80vh !important;
}

.hero-left {
    max-width: 600px !important;
}

.hero-badge {
    display: inline-flex !important;
    align-items: center !important;
    gap: 10px !important;
    background: linear-gradient(135deg, rgba(25,118,210,0.1) 0%, rgba(74,74,138,0.1) 100%) !important;
    padding: 12px 24px !important;
    border-radius: 50px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    margin-bottom: 2rem !important;
    border: 1px solid rgba(25,118,210,0.2) !important;
    color: #1976D2 !important;
}

.hero-title {
    font-size: 3.8rem !important;
    font-weight: 900 !important;
    color: #333 !important;
    margin-bottom: 1.5rem !important;
    line-height: 1.1 !important;
}

.hero-title .highlight {
    background: linear-gradient(135deg, #1976D2 0%, #2196F3 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    position: relative !important;
}

.hero-title .highlight::after {
    content: '' !important;
    position: absolute !important;
    bottom: -5px !important;
    left: 0 !important;
    width: 100% !important;
    height: 4px !important;
    background: linear-gradient(90deg, #1976D2, #2196F3) !important;
    border-radius: 2px !important;
}

.hero-description {
    font-size: 1.2rem !important;
    color: #666 !important;
    line-height: 1.7 !important;
    margin-bottom: 2.5rem !important;
}

.hero-features {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1rem !important;
    margin-bottom: 2.5rem !important;
}

.feature-item {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    color: #333 !important;
}

.feature-item i {
    color: #1976D2 !important;
    font-size: 1rem !important;
}

.hero-buttons {
    display: flex !important;
    gap: 1.5rem !important;
    flex-wrap: wrap !important;
    margin-bottom: 3rem !important;
}

.hero-stats {
    display: flex !important;
    gap: 2rem !important;
    margin-top: 2rem !important;
}

.hero-stats .stat-item {
    text-align: center !important;
}

.hero-stats .stat-number {
    font-size: 2.5rem !important;
    font-weight: 900 !important;
    background: linear-gradient(135deg, #1976D2 0%, #2196F3 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    display: block !important;
    margin-bottom: 0.5rem !important;
}

.hero-stats .stat-label {
    font-size: 0.9rem !important;
    color: #666 !important;
    font-weight: 600 !important;
}

/* Hero Visual */
.hero-right {
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.hero-visual {
    position: relative !important;
    width: 100% !important;
    max-width: 500px !important;
}

.hero-mockup {
    background: #ffffff !important;
    border-radius: 20px !important;
    box-shadow: 0 25px 80px rgba(0,0,0,0.15) !important;
    overflow: hidden !important;
    transform: perspective(1000px) rotateY(-15deg) rotateX(5deg) !important;
    transition: transform 0.3s ease !important;
}

.hero-mockup:hover {
    transform: perspective(1000px) rotateY(-10deg) rotateX(2deg) !important;
}

.mockup-header {
    background: #f5f5f5 !important;
    padding: 15px 20px !important;
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

.mockup-dots {
    display: flex !important;
    gap: 8px !important;
}

.dot {
    width: 12px !important;
    height: 12px !important;
    border-radius: 50% !important;
}

.dot.red { background: #ff5f56 !important; }
.dot.yellow { background: #ffbd2e !important; }
.dot.green { background: #27ca3f !important; }

.mockup-url {
    background: #ffffff !important;
    padding: 5px 15px !important;
    border-radius: 15px !important;
    font-size: 0.8rem !important;
    color: #666 !important;
    border: 1px solid #e0e0e0 !important;
}

.mockup-content {
    padding: 20px !important;
}

.mockup-nav {
    display: flex !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
}

.nav-item {
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    color: #666 !important;
}

.nav-item.active {
    background: #1976D2 !important;
    color: white !important;
}

.mockup-hero {
    text-align: center !important;
    margin-bottom: 25px !important;
}

.mockup-title {
    font-size: 1.2rem !important;
    font-weight: 800 !important;
    color: #333 !important;
    margin-bottom: 8px !important;
}

.mockup-subtitle {
    font-size: 0.8rem !important;
    color: #666 !important;
    margin-bottom: 15px !important;
}

.mockup-button {
    background: #1976D2 !important;
    color: white !important;
    padding: 8px 20px !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    display: inline-block !important;
}

.mockup-features {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 10px !important;
}

.feature-box {
    height: 60px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%) !important;
    border-radius: 8px !important;
    border: 1px solid #e0e0e0 !important;
}

/* Floating Elements */
.floating-elements {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
}

.floating-element {
    position: absolute !important;
    width: 60px !important;
    height: 60px !important;
    background: linear-gradient(135deg, #1976D2 0%, #2196F3 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.5rem !important;
    box-shadow: 0 10px 30px rgba(25, 118, 210, 0.3) !important;
    animation: float 6s ease-in-out infinite !important;
}

.element-1 {
    top: 10% !important;
    right: -10% !important;
    animation-delay: 0s !important;
}

.element-2 {
    top: 30% !important;
    left: -15% !important;
    animation-delay: 1.5s !important;
}

.element-3 {
    bottom: 30% !important;
    right: -5% !important;
    animation-delay: 3s !important;
}

.element-4 {
    bottom: 10% !important;
    left: -10% !important;
    animation-delay: 4.5s !important;
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg) !important; 
        opacity: 0.8 !important;
    }
    50% { 
        transform: translateY(-20px) rotate(180deg) !important; 
        opacity: 1 !important;
    }
}

/* Responsive */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr !important;
        gap: 3rem !important;
        text-align: center !important;
    }
    
    .hero-mockup {
        transform: none !important;
    }
    
    .floating-element {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }
    
    .hero-features {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }
    
    .hero-stats {
        flex-direction: column !important;
        gap: 1rem !important;
    }
}
