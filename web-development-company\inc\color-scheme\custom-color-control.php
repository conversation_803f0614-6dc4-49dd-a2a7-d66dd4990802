<?php 

$web_development_company_custom_icon = get_theme_mod('web_development_company_custom_icon');
$web_development_company_color_scheme_gradiant2 = get_theme_mod('web_development_company_color_scheme_gradiant2');

$web_developer_color_scheme_css = "";
$web_development_company_color_scheme_css = "";

if($web_development_company_custom_icon != true ){

  $web_developer_color_scheme_css .='.meta-feilds{';

  $web_developer_color_scheme_css .='background: none';

  $web_developer_color_scheme_css .='.services_inner_box h3{';

  $web_developer_color_scheme_css .='padding-top:20px !important';

 $web_developer_color_scheme_css .='}';

 
}

$web_development_company_color_scheme_css .= '.service-btn a, .breadcrumb a, .site-main .wp-block-button__link, .postsec-list .wp-block-button__link, .slide-btn a, .service-btn a, .services_inner_box:hover, #button, .meta-feilds,.banner-btn a, .pagemore a, .serv-btn a, .woocommerce ul.products li.product .button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce a.button, .woocommerce button.button, .woocommerce #respond input#submit, #commentform input#submit, .services_inner_box:hover, #commentform input#submit,  button.wc-block-components-checkout-place-order-button, .wc-block-components-totals-coupon__button.contained {';
$web_development_company_color_scheme_css .= 'background-image: linear-gradient(0deg, '.esc_attr($web_developer_color_scheme_gradiant1).', '.esc_attr($web_development_company_color_scheme_gradiant2).') !important;';
$web_development_company_color_scheme_css .= '}';

if ($web_developer_color_scheme_gradiant1) {
  $web_development_company_color_scheme_css .= ':root {';
  $web_development_company_color_scheme_css .= '--first-theme-color: ' . esc_attr($web_developer_color_scheme_gradiant1) . ' !important;';
  $web_development_company_color_scheme_css .= '} ';
}

if ($web_development_company_color_scheme_gradiant2) {
  $web_development_company_color_scheme_css .= ':root {';
  $web_development_company_color_scheme_css .= '--second-theme-color: ' . esc_attr($web_development_company_color_scheme_gradiant2) . ' !important;';
  $web_development_company_color_scheme_css .= '} ';
}
