=== Web Development Company ===
Contributors: classictemplate
Tags: blog, e-commerce, portfolio, one-column, two-columns, three-columns, four-columns, grid-layout, left-sidebar, right-sidebar, custom-logo, post-formats, translation-ready, full-width-template, footer-widgets, featured-images, custom-colors, editor-style, wide-blocks, block-styles, custom-header, custom-background, custom-menu, sticky-post, threaded-comments, theme-options, rtl-language-support
Requires at least: 5.0
Tested up to: 6.8
Requires PHP: 5.6
Stable tag: 1.9.2
License: GNU General Public License
License URI: http://www.gnu.org/licenses/gpl.html

Web Development Company WordPress theme is a purpose-built theme specifically designed for web development companies and agencies. It offers a comprehensive set of features and functionalities to showcase the services and expertise of web development businesses engagingly and professionally.

== Description ==

Web Development Company WordPress theme is a purpose-built theme specifically designed for web development companies and agencies. It offers a comprehensive set of features and functionalities to showcase the services and expertise of web development businesses engagingly and professionally. The theme is build for business and niches specifically for Web Design, SEO Services, Digital Agency, Software Development, IT Consulting, E-commerce, App Development, UX/UI Design, Online Marketing, Cloud Solutions, Tech Startups, Creative Agency, Freelance Web, SaaS Providers, Web Hosting. 	With its modern design, Web Development Company provides a visually appealing platform to highlight the capabilities and portfolio of web development companies. The theme offers multiple pre-designed layouts and templates that can be easily customized to match the branding and style of the company. It allows for the seamless integration of logos, color schemes, and typography, ensuring a consistent and cohesive representation of the brand. Furthermore, the theme offers dedicated sections and layouts to present past projects, client testimonials, and success stories. This enables web development companies to demonstrate their expertise and capabilities, giving potential clients a clear understanding of their quality of work. The theme also includes sections highlighting services, team members, and client logos, providing a comprehensive overview of the company’s offerings and credentials. The integration of contact forms and call-to-action buttons facilitates easy communication and encourages visitors to reach out for inquiries or project discussions. In addition, the Web Development Company theme is fully responsive and mobile-friendly. It ensures that the website looks and functions flawlessly on various devices and screen sizes, providing an optimal user experience for visitors accessing the site from desktops, tablets, or smartphones.

== Changelog ==

= 1.0 =
* Initial Version Released.

= 1.1 =
* Added footer link.
* Added about theme info.
* Added get started.
* Designed a new layout for the blog page.

= 1.1.1 =
* Done post pagination css.
* Changes done in copyright.
* Added documentation link in theme info.

= 1.1.2 =
* Done clearing floats page css.
* Added post formates.
* Added post content options.
* Added theme sidebar layout.
* Added logo resizer option.
* Added default color for slider.

= 1.1.3 =
* Resolved css error for block buttons.

= 1.1.4 =
* Removed font prefixing from function.php.

= 1.1.5 =
* Resolved post metabox error.

= 1.1.6 =
* Done with breadcrumb CSS.
* Corrected the condition of the slider and the services section. 
* Added show/hide footer option.
* Fixed font-family enqueue error in function.php.

= 1.1.7 =
* Resolved shop page button errors.

= 1.1.8 =
* Done cart page css.
* Done checkout page css.

= 1.1.9 =
* Added permalink in titles.
* Added slider button link settings.
* Resolved slider button code errors from template-home-page.php.

= 1.2 =
* Changed theme url.

= 1.3 =
* Done prefixing for variables.

= 1.4 =
* Done prefixing for variables.
* Added condition in slider code.
* Added hover on home page.

= 1.5 =
* Resolved slider button link error.
* Added condition for service section.

= 1.6 =
* Changes done in slider button.
* Updated WordPress version.

= 1.7 =
* Resolved button css error.

= 1.8 =
* Changed the urls for the theme.

= 1.8.1 =
* Added default image for slider
* Added default size for slider in customizer.
* Added css for slider for slider height.
* Added latest bootstrap version.
* Added css in media for container width.
* Added css for shop page product in media.
* Done homepage css.

= 1.8.2 =
* Added scroll to top positions option in customizer.
* Added footer background image option in customizer.
* Added post page thumbnail on off setting in customizer.
* Added slider height option in customizer.

= 1.8.3 =
* Updated woocommerce archive product template.
* Added footer background color option in customizer.
* Added blog post page image box shadow option in customizer.
* Added woocommerce product image border radius option in customizer.

= 1.8.4 =
* Added upgraded features in site layout section, header section and post section.
* Added woocommerce products per row option in customizer.
* Added woocommerce products per page option in customizer.
* Added show/hide shop page sidebar.
* Added show/hide single product page sidebar.
* Added single product page sidebar option.
* Resolved css for upgrade pro button in about web development company.
* Resolved css for go to premium button in theme info.
* Resolved error in scroll to top.
* Resolved error in shop page sidebar.
* Changed the urls for the theme.
* Tested upto WP v6.6

= 1.8.5 =
* Added global color in customizer.
* Added upgraded features in global color section.
* Changed the urls for theme page.

= 1.8.6 =
* Resolved copyright link error in customizer.

= 1.8.7 =
* Resolved css error in sidebar search button.
* Tested upto WP v6.7

= 1.8.8 =
* Resolved css error in shop page.
* Resolved css error in single product page.
* Resolved css error in customizer.

= 1.8.9 =
* Added footer widget in footer section.
* Added css for myaccount address button.

= 1.9 =
* Resolved error in footer widget.
* Resolved global color error.

= 1.9.1 =
* fixed alt attribute issue.

= 1.9.2 =
* Added scroll to top button text option in customizer.
* Resolved error in function.php
* Tested upto WP v6.8
* Added theme demo import.

Web Development Company WordPress Theme, Copyright 2023 classictemplate
Web Development Company is distributed under the terms of the GNU GPL.

Web Development Company WordPress Theme is child theme of Web Developer WordPress Theme, Copyright 2023 classictemplate (theclassictemplates.com)
Web Developer WordPress Theme is distributed under the terms of the GNU GPL

Web Development Company is a child theme of Web Developer and as such uses the same opensource projects as its parent theme.

Pxhere Images,
License: CC0 1.0 Universal (CC0 1.0)
Source: https://pxhere.com/en/license

Slider Image, Copyright
License: CC0 1.0 Universal (CC0 1.0)
Source: https://pxhere.com/en/photo/633724

Slider Image, Copyright
License: CC0 1.0 Universal (CC0 1.0)
Source: https://pxhere.com/en/photo/1450321

Slider Image, Copyright
License: CC0 1.0 Universal (CC0 1.0)
Source: https://pxhere.com/en/photo/1447919

== Homepage Installation ==

Step-1. Go to Dashboard -- Pages -- Add new page -- Add page attribute (Home Page) -- Publish it.
Step-2. Go to Dashboard -- Settings -- Reading -- Click on static page -- Select home page -- Save changes.

Slider section
Step-1. Go to Dashboard -- Post -- Add new category -- Publish it.
Step-2. Go to Dashboard -- Post -- Add new post -- Add content and featured image -- Publish it.
Step-3. Go to Dashboard -- Appearnace -- Customize -- Theme option panel -- Mange Slider Section -- Select the Category -- Publish it.

Services section
Step-1. Go to Dashboard -- Post -- Add new category -- Publish it.
Step-2. Go to Dashboard -- Post -- Add new post -- Add content -- Add meta field Icon class -- Publish it.
Step-3. Go to Dashboard -- Appearnace -- Customize -- Theme option panel -- Manage Service Section
 -- Select the category -- Publish it.

Footer section
Step-1. Go to Dashboard -- Appearnace -- Customize -- Theme option panel -- Manage Footer section -- Add copyright text -- Publish it.

== Theme Changes ==

* Header Changes.
* Slider Changes.
* Color Changes.
* Features Design Changes.