/* Demo Importer CSS */
.wrapper-info {
    text-align: center;
    max-width: 100%;
    background-color: #0d223e;
    opacity: 0.9;
    padding:3.3em 5em;
    box-shadow: 0 10px 20px 0 rgb(94 44 237 / 20%);
    color: #fff;
    font-size: 15px;
}
.wrapper-info img {
    margin-bottom: 25px;
    width: 150px;
}
span.version {
    font-size: 15px;
    font-weight: normal;
}
.wrapper-info p{
    font-size: 15px;
}
.theme-offer p {
    color: red;
    font-size: 12px;
    padding: 0 5em;
    margin: 30px 0;
}
.success {
    margin-top: 35px;
    color: #000;
    font-weight: 800;
    font-size: 24px;
    -webkit-animation: bounce .4s ease infinite alternate;
}
.theme-id-container .theme-browser .theme .theme-name {
    height: 48px;
}
.start-box{
    margin-top: 20px;
}
.start-box .success{
    color: #fff;
}
.start-box .columns-wrapper .column {
	padding-right: 1em !important;
}
.start-box p{
	font-size: 15px;
	color: #fff;
}
.start-box #setting-error-tgmpa p{
    color: #000;
}
.start-box b{
	font-weight: bold !important;
}
.start-box h2 {
    font-size: 25px;
    color: #fff;
    padding: 15px;
	line-height: 1.2;
    margin: 0;
    margin-bottom: 10px;
    border-radius: 10px;
    background-image: linear-gradient(to right, #4734c0, #8472f7);
}
.start-box .button-large{
	background-color: #fff !important;
    border-color: #fff !important;
    color: #000 !important;
    font-weight: bold;
    padding: 4px 30px !important;
    font-size: 15px;
}

.start-box .columns-wrapper {
	margin-right: 0;
}
.start-box .columns-wrapper .column {
	float: left;
	box-sizing: border-box;
	padding-right: 4em;
}
.start-box .columns-wrapper .column-half {
	width: 50%;
}
.start-box .get-screenshot img{
	width: 100%;
    height: 612px !important;
}
.start-box .wrapper-info{
    min-height: 513px !important;
}

@-webkit-keyframes bounce {
  0% {
      text-shadow:
                  0 2px 3px rgba(0, 0, 0, 1);  
  }
  100% {
    transform:translateY(-20px);
  }
}
